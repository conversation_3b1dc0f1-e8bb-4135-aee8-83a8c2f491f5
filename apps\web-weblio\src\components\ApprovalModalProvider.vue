
<script setup lang="ts">
import EditForm from '#/views/modules/tSearch/components/EditForm.vue';
import { useApprovalModal } from './useApprovalModal';
import { ref, watch } from 'vue';

const formRef = ref<any>(null);

// 1. 获取全局弹窗状态和控制器
const { isVisible, currentItem, onSuccessCallback, closeModal } = useApprovalModal();

// 2. 定义成功处理函数
const handleSuccess = () => {
  // a. 如果有成功回调，则执行（例如：刷新列表）
  if (onSuccessCallback.value) {
    onSuccessCallback.value();
  }
  // b. 关闭弹窗
  closeModal();
};

// 3. 监听 isVisible 状态，当其变为 true 时，调用 EditForm 的 open 方法
watch(isVisible, (newVal) => {
  if (newVal && currentItem.value) {
    // 使用 nextTick 可能更稳定，但通常 ref 能直接工作
    formRef.value?.open(currentItem.value);
  }
});

</script>

<template>
  <!-- 
    使用 v-if="isVisible" 确保只在需要时渲染 EditForm 组件及其依赖项，
    这是一种优化，避免了在不可见时占用资源。
    当 isVisible 变为 false 时，组件会被销毁。
    当 isVisible 再次变为 true 时，会创建一个全新的 EditForm 实例，
    从而天然地隔离了每次打开弹窗的状态，确保了数据的纯净性。
  -->
  <EditForm
    v-if="isVisible"
    ref="formRef"
    @success="handleSuccess"
    @close="closeModal" 
    @cancel="closeModal"
  />
</template>
