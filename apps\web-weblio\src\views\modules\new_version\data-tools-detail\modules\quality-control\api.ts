import { requestClient } from '#/api/request';

// 质量控制相关API接口 - 基于标准化接口文档

/**
 * 数据抽取接口 - 用于质量控制数据处理
 * 支持文件上传和数据参数
 * @param data FormData containing files and data parameters
 */
export async function extractData(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/extract', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 120_000,
  });
}

/**
 * JSON文件下载接口
 * @param download_url 下载URL路径
 */
export async function downloadFile(download_url: string) {
  return requestClient.get(`/rgdc-sys/dataTools/download/${download_url}`, {
    responseType: 'blob',
  });
}

/**
 * 图像查看接口
 * 返回Base64编码的图像数据
 * @param view_url 图像URL路径
 */
export async function viewImage(view_url: string) {
  return requestClient.get('/rgdc-sys/dataTools/view', {
    params: { view_url },
  });
}

/**
 * 获取整编字典映射信息 - 用于质量控制字段配置
 */
export async function getFieldDict() {
  return requestClient.get('/rgdc-sys/dataTools/get_field_dict');
}

/**
 * 获取整编任务列表 - 用于质量控制任务管理
 * @param params 查询参数
 */
export async function getTasksList(params: {
  page: number;
  page_size: number;
  task_name?: string;
}) {
  return requestClient.get('/rgdc-sys/dataTools/tasks_list', {
    params,
  });
}

/**
 * 获取整编任务详情 - 用于质量控制任务详情
 * @param id 任务ID
 */
export async function getTasksDetail(id?: number) {
  return requestClient.get('/rgdc-sys/dataTools/tasks_detail', {
    params: id ? { id } : {},
  });
}

/**
 * 下载整编Excel表格模板 - 用于质量控制模板
 */
export async function downloadTemplate() {
  return requestClient.get('/rgdc-sys/dataTools/download_template', {
    responseType: 'blob',
  });
}

// 兼容性函数 - 保持向后兼容
/**
 * 开始质量控制 (兼容性函数)
 * @param data 质量控制参数
 */
export async function startQualityControl(data: FormData) {
  return extractData(data);
}

/**
 * 上传Excel文件 (兼容性函数)
 * @param file 文件
 */
export async function uploadExcelFile(file: FormData) {
  return extractData(file);
}

/**
 * 获取质量控制结果 (兼容性函数)
 * @param taskId 任务ID
 */
export async function getQualityControlResult(taskId: string) {
  return getTasksDetail(parseInt(taskId));
}

/**
 * 下载质量控制报告 (兼容性函数)
 * @param taskId 任务ID或下载URL
 */
export async function downloadQualityReport(taskId: string) {
  return downloadFile(taskId);
}

/**
 * 获取元数据字段配置 (兼容性函数)
 */
export async function getMetadataFields() {
  return getFieldDict();
}
