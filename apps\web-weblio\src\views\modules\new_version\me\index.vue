<script setup lang="ts">
import type { NotificationItem } from '@vben/layouts';

import { msgListByPageApi } from '#/api';
import { usePermissions } from '#/views/modules/new_version/hooks/usePermissions';
import { useUserStore } from '@vben/stores';
import { BuildingIcon, StarIcon, UserIcon } from 'tdesign-icons-vue-next';
import {
  Avatar,
  Button,
  Card,
  Empty,
  NotifyPlugin,
  TabPanel,
  Tabs,
} from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import {
  deleteBatch,
  getMyDownloadData,
  getMyFavoritesData,
  getMyRecentlyViewed,
} from './api';
import Notification from './modules/notification/notification.vue';
import HistoryTask from './modules/workflow/history-task.vue';
import InquiryUnderApproval from './modules/workflow/inquiry-under-approval.vue';
import MyTask from './modules/workflow/my-task.vue';

const router = useRouter();

const notifications = ref<NotificationItem[]>([]);
const loadData = async () => {
  const res: any = await msgListByPageApi({
    pageSize: 5,
    pageNum: 1,
    sorts: [],
  });

  notifications.value = res.records.map((item: any) => {
    return {
      avatar: '/mail.png',
      date: item.createTime,
      isRead: item.readStatus,
      message: item.content,
      title: item.title,
    };
  });
};
// 我的收藏数据
const myCollections = ref<any>();
// 我的下载数据
const myDownloads = ref();

// 最近浏览数据
const recentContent = ref();
const userStore = useUserStore();
const userInfo: any = computed(
  () =>
    userStore.userInfo || {
      name: '未知',
      userType: '未知',
      organization: '未知',
      location: '未知',
    },
);
const handleNoticeClear = async () => {
  notifications.value = [];
};
const initFavorites = async () => {
  const res: any = await getMyFavoritesData({
    current: 1,
    pageSize: 6,
    param: {},
    sorts: [],
  });
  myCollections.value = res.records;
};
const initDownload = async () => {
  const res = await getMyDownloadData({
    createdBy: userInfo.value.realName,
    current: 1,
    pageSize: 6,
  });
  myDownloads.value = res;
};
const initRecentlyViewed = async () => {
  const res: any = await getMyRecentlyViewed({
    createdBy: userInfo.value.realName,
    current: 1,
    pageSize: 6,
  });
  recentContent.value = res.records;
};
const deleteFavorites = async (id: string) => {
  try {
    await deleteBatch(id);
    NotifyPlugin.success({
      title: '取消收藏成功',
      duration: 2000,
      closeBtn: true,
    });
    await initFavorites(); // 删除成功后刷新收藏列表
  } catch (error) {
    NotifyPlugin.error({
      title: '取消收藏失败',
      content: error?.message || '操作失败，请稍后重试',
      duration: 3000,
      closeBtn: true,
    });
  }
};
const init = () => {
  initFavorites();
  initDownload();
  initRecentlyViewed();
  loadData();
};

onMounted(() => {
  init();
});
const { handleResourceAccess } = usePermissions(init);
// 根据用户名获取头像显示内容
const avatarContent = computed(() => {
  const name = userInfo.value?.realName;
  return typeof name === 'string' && name.length > 0 ? name.charAt(0) : '用';
});

// 统一的卡片点击处理
function handleCardClick(item: any) {
  const newItem = { ...item };
  const { dataInformation } = newItem;

  // 解析 dataInformation
  if (dataInformation && typeof dataInformation === 'string') {
    try {
      const data = JSON.parse(dataInformation);
      newItem.infomation = data;
    } catch (error) {
      console.error('Failed to parse dataInformation:', error);
    }
  }

  // 兼容处理 baseCode
  if (newItem.operationCode) {
    newItem.baseCode = newItem.operationCode;
  }

  // 调用 hook 中的权限处理方法
  handleResourceAccess(newItem);
}

// 收藏星星点击事件
function handleStarClick(item) {
  const { id } = item;
  deleteFavorites(id);
}
const handleMoreClick = () => {
  router.push({ path: '/tPortalFavoritesIndex' });
};
</script>

<template>
  <div class="user-profile">
    <!-- 头部区域 -->
    <div class="profile-header">
      <div class="avatar-section">
        <div class="avatar-container">
          <Avatar size="120px" class="user-avatar">
            {{ avatarContent }}
          </Avatar>
        </div>
      </div>

      <div class="user-info">
        <h2 class="user-name">{{ userInfo.realName }}</h2>
        <div class="user-meta-bar">
          <div class="meta-item">
            <UserIcon class="meta-icon" />
            <span>{{ userInfo.sourceSystem }}</span>
          </div>
          <div class="meta-item">
            <BuildingIcon class="meta-icon" />
            <span>{{ userInfo.deptName }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 左侧Tab内容 -->
      <div class="content-section content-section-has-btn">
        <Button
          variant="text"
          class="more-btn tab-extra-btn"
          @click="handleMoreClick"
        >
          查看更多
        </Button>
        <Tabs default-value="collections" class="user-tabs">
          <TabPanel value="collections" label="我的收藏">
            <div
              class="content-grid"
              v-if="myCollections && myCollections.length > 0"
            >
              <Card
                v-for="(item, index) in myCollections"
                :key="index"
                class="content-card"
                hover
                @click="handleCardClick(item)"
              >
                <template #actions>
                  <button class="star-btn" @click.stop="handleStarClick(item)">
                    <StarIcon class="star-icon" />
                  </button>
                </template>
                <div class="card-content">
                  <div class="content-icon">
                    <img :src="item.dataImg" alt="缩略图" class="item-thumb" />
                  </div>
                  <div class="content-info">
                    <h4 class="content-title">{{ item.dataName }}</h4>
                    <div class="content-meta">
                      <!-- <span class="content-type">{{ item.dataDescri }}</span> -->
                    </div>
                    <div class="content-stats">
                      <span class="stat">👁 {{ item.browsingCount }}</span>
                      <!-- <span class="stat">⬇ {{ item.downloadCount }}</span> -->
                      <span v-if="+item.dataAuth" class="public-tag">公开</span>
                      <span class="date"> {{ item.createTime }}</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
            <div v-else class="empty-tip">
              <Empty />
            </div>
          </TabPanel>

          <TabPanel value="downloads" label="我的下载" v-if="!1">
            <div
              class="content-grid"
              v-if="myDownloads && myDownloads.length > 0"
            >
              <Card
                v-for="(item, index) in myDownloads"
                :key="index"
                class="content-card"
                hover
              >
                <div class="card-content">
                  <div class="content-icon">
                    <img :src="item.dataImg" alt="缩略图" class="item-thumb" />
                  </div>
                  <div class="content-info">
                    <h4 class="content-title">{{ item.dataName }}</h4>
                    <div class="content-meta">
                      <span class="content-type">{{ item.dataDescri }}</span>
                    </div>
                    <div class="content-stats">
                      <span class="stat">👁 {{ item.browsingCount }}</span>
                      <span class="stat">⬇ {{ item.downloadCount }}</span>
                      <span v-if="+item.dataAuth" class="public-tag">公开</span>
                      <span class="date">📅 {{ item.createTime }}</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
            <div v-else class="empty-tip">
              <Empty />
            </div>
          </TabPanel>
        </Tabs>
        <Tabs default-value="0" v-if="userInfo.realName">
          <TabPanel value="0" label="审批中查询">
            <InquiryUnderApproval />
          </TabPanel>
          <TabPanel value="1" label="等待我审批">
            <MyTask />
          </TabPanel>
          <TabPanel value="2" label="已审批记录">
            <HistoryTask />
          </TabPanel>
        </Tabs>
      </div>
      <!-- 右侧最近浏览 -->
      <div class="recent-section">
        <div class="section-header">
          <h3>最近浏览</h3>
        </div>
        <div
          class="recent-list"
          v-if="recentContent && recentContent.length > 0"
        >
          <div
            v-for="(item, index) in recentContent"
            :key="index"
            class="recent-item"
            @click="handleCardClick(item)"
          >
            <div class="recent-icon">
              <img :src="item.dataImg" alt="缩略图" class="item-thumb" />
            </div>
            <div class="recent-info">
              <div class="recent-title">{{ item.dataName }}</div>
              <div class="recent-date">{{ item.createTime }}</div>
            </div>
          </div>
        </div>
        <div v-else class="empty-tip">
          <Empty />
        </div>
        <div class="message-section" v-if="userInfo.realName">
          <div class="section-header">
            <h3>消息通知</h3>
          </div>
          <Notification
            :dot="false"
            :notifications="notifications"
            @clear="handleNoticeClear"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.message-section {
  margin-top: 24px;
}
.user-profile {
  min-height: 100vh;
  background: #f5f5f5;
}

.profile-header {
  width: 100vw;
  min-width: 1200px;
  background: var(--td-brand-color, #1e88e5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  padding-top: 56px;
  padding-bottom: 36px;
  margin-left: 50%;
  transform: translateX(-50%);
  box-sizing: border-box;
}

.avatar-section {
  margin-bottom: 12px;
}

.user-avatar {
  border: 4px solid rgba(255, 255, 255, 0.3);
  font-size: 56px;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  text-align: center;
  color: #fff;
}

.user-name {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 10px 0;
  color: #fff;
  letter-spacing: 2px;
}

.user-meta-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  margin-top: 8px;
  margin-bottom: 0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 17px;
  color: #fff;
  font-weight: 500;
}

.meta-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #fff;
}

.user-details {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);

  .icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

.content-area {
  width: 100%;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 32px;
  transform: translateY(-20px);
  box-sizing: border-box;
  align-items: start;
  min-height: unset;
}

.content-section,
.recent-section {
  display: block;
  min-height: unset;
  height: auto;
  flex: unset;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  max-height: 600px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--td-brand-color) #f0f0f0;
}

.content-grid::-webkit-scrollbar {
  width: 6px;
  background: #f0f0f0;
}
.content-grid::-webkit-scrollbar-thumb {
  background: var(--td-brand-color);
  border-radius: 4px;
}

@media (max-width: 1024px) {
  .content-area {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 8px;
  }
}

.content-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  max-height: 600px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--td-brand-color) #f0f0f0;
}

.content-grid::-webkit-scrollbar {
  width: 6px;
  background: #f0f0f0;
}
.content-grid::-webkit-scrollbar-thumb {
  background: var(--td-brand-color);
  border-radius: 4px;
}

@media (max-width: 900px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    max-height: 320px;
  }
}

.recent-section {
  background: white;
  border-radius: 12px;
  padding: 24px 24px 12px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.recent-section .section-header {
  margin-bottom: 16px;
  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0;
    letter-spacing: 1px;
  }
}

.tab-footer {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.user-tabs {
  .t-tabs__nav {
    margin-bottom: 20px;
  }

  .t-tabs__nav-item {
    font-size: 16px;
    font-weight: 500;
    color: #666;

    &.t-is-active {
      color: var(--td-brand-color);
      font-weight: 600;
    }
  }

  .t-tabs__nav-item-wrapper {
    padding: 8px 16px;
  }

  .t-tabs__content {
    padding-top: 0;
  }

  .t-tab-panel {
    padding: 0;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    color: #333;
  }
}

.more-btn {
  color: var(--td-brand-color);
  font-size: 14px;
}

.content-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.card-content {
  display: flex;
  gap: 20px;
  padding: 16px;
  align-items: stretch; // 让图片和内容等高
  min-height: 90px;
}

.content-icon {
  width: 80px; // 固定宽度
  height: 100%; // 高度自适应内容区
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f8ff;
  border-radius: 8px;
  flex-shrink: 0;
}

.content-info {
  flex: 1;
}

.content-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px 0;
  color: #333;
  line-height: 1.4;
}

.content-meta {
  margin-bottom: 12px;
}

.content-type {
  background: #e3f2fd;
  color: var(--td-brand-color);
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.content-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.public-tag {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.date {
  color: #999;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 600px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--td-brand-color) #f0f0f0;
}

.recent-list::-webkit-scrollbar {
  width: 6px;
  background: #f0f0f0;
}
.recent-list::-webkit-scrollbar-thumb {
  background: var(--td-brand-color);
  border-radius: 4px;
}

@media (max-width: 900px) {
  .recent-list {
    max-height: 220px;
  }
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 14px 16px;
  background: #f7f9fa;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  transition:
    box-shadow 0.2s,
    background 0.2s;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  &:hover {
    background: #e6f0fa;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-color: var(--td-brand-color);
  }
}

.recent-icon {
  width: 36px;
  height: 36px;
  background: #e3f2fd;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  .recent-icon-svg {
    width: 22px;
    height: 22px;
    color: var(--td-brand-color);
  }
}

.recent-info {
  flex: 1;
  min-width: 0;
}

.recent-title {
  font-size: 15px;
  font-weight: 500;
  color: #222;
  margin-bottom: 2px;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-date {
  font-size: 12px;
  color: #8c8c8c;
}

.star-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  z-index: 2;
  padding: 0;
  transition: transform 0.15s;
}
.star-btn:hover .star-icon {
  color: var(--td-brand-color);
  transform: scale(1.15);
}
.star-icon {
  color: #e6a23c;
  font-size: 22px;
  transition:
    color 0.2s,
    transform 0.2s;
}

.user-tabs-flex {
  width: 100%;
}
.tabs-bar-row {
  display: flex;
  align-items: center;
  width: 100%;
}
.user-tabs {
  flex: 1 1 auto;
}
.more-btn.tab-extra-btn {
  margin-left: 16px;
  font-size: 15px;
  color: var(--td-brand-color);
  font-weight: 500;
  padding: 0 8px;
  height: 40px;
  display: flex;
  align-items: center;
}
@media (max-width: 600px) {
  .tabs-bar-row {
    flex-direction: column;
    align-items: stretch;
  }
  .more-btn.tab-extra-btn {
    margin: 8px 0 0 0;
    width: 100%;
    justify-content: flex-end;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .content-area {
    padding: 0 16px;
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .user-details {
    flex-direction: column;
    gap: 12px;
  }

  .card-content {
    flex-direction: column;
    text-align: center;
  }

  .content-icon {
    align-self: center;
  }
  .recent-section {
    padding: 16px;
  }
  .recent-list {
    gap: 10px;
  }
  .recent-item {
    padding: 10px 8px;
    gap: 8px;
  }
  .recent-title {
    font-size: 14px;
  }
}

@media (max-width: 600px) {
  .user-meta-bar {
    gap: 16px;
    flex-direction: column;
  }
  .meta-item {
    font-size: 15px;
  }
  .meta-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

.content-section-has-btn {
  position: relative;
}
.more-btn.tab-extra-btn {
  position: absolute;
  top: 16px;
  right: 24px;
  z-index: 3;
  font-size: 15px;
  color: var(--td-brand-color);
  font-weight: 500;
  padding: 0 8px;
  height: 40px;
  display: flex;
  align-items: center;
}
@media (max-width: 600px) {
  .more-btn.tab-extra-btn {
    position: static;
    margin: 8px 0 0 0;
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 900px) {
  .profile-header {
    min-width: 0;
    width: 100vw;
    padding-top: 32px;
    padding-bottom: 24px;
  }
  .user-avatar {
    font-size: 36px;
    width: 80px;
    height: 80px;
  }
  .user-name {
    font-size: 22px;
  }
  .user-meta-bar {
    gap: 16px;
    flex-direction: column;
    font-size: 15px;
  }
  .meta-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

.item-thumb {
  width: 100%;
  height: 100%;
  object-fit: contain; // 保证图片不变形
  border-radius: 8px;
  background: #f0f0f0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  display: block;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #b0b0b0;
  font-size: 16px;
  min-height: 180px;
  background: #f7f9fa;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  .empty-icon {
    font-size: 48px;
    color: #e6a23c;
    margin-bottom: 12px;
    opacity: 0.6;
    width: 48px;
    height: 48px;
    object-fit: contain;
  }
}
.approval-dialog-content {
  padding: 20px;
  text-align: left;
}
.approval-dialog-content p {
  margin-bottom: 10px;
  font-size: 15px;
}
.approval-dialog-content .t-radio-group {
  margin-top: 10px;
}
</style>
