import { requestClient } from '#/api/request';

/**
 * 数据抽取接口 - 用于PDF文件解析和数据抽取
 * 支持文件上传和数据参数
 * @param data FormData containing files and data parameters
 */
export async function extractData(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/extract', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 120_000, // 2分钟超时
  });
}

/**
 * 兼容性函数 - 上传PDF文件进行解析
 * @param data FormData containing PDF files
 * @param taskName 任务名称
 */
export async function uploadPdfs(data: FormData, taskName: string) {
  // 添加任务名称到FormData中
  data.append('data', JSON.stringify({ task_name: taskName }));
  return extractData(data);
}

/**
 * JSON文件下载接口
 * @param download_url 下载URL路径
 */
export async function downloadFile(download_url: string) {
  return requestClient.get(`/rgdc-sys/dataTools/download/${download_url}`, {
    responseType: 'blob',
  });
}

/**
 * 兼容性函数 - 通过session ID下载文件
 * @param sessionId session ID
 */
export async function downloadFileBySessionId(sessionId: string) {
  return downloadFile(sessionId);
}

/**
 * 图像查看接口
 * 返回Base64编码的图像数据
 * @param view_url 图像URL路径
 */
export async function viewImage(view_url: string) {
  return requestClient.get('/rgdc-sys/dataTools/view', {
    params: { view_url },
  });
}
