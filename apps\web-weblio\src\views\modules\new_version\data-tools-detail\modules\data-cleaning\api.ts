import { requestClient } from '#/api/request';

// 数据汇聚与清洗相关API接口 - 基于标准化接口文档

/**
 * 数据抽取接口 - 用于数据清洗和处理
 * 支持文件上传和数据参数
 * @param data FormData containing files and data parameters
 */
export async function extractData(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/extract', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 120_000,
  });
}

/**
 * JSON文件下载接口
 * @param download_url 下载URL路径
 */
export async function downloadFile(download_url: string) {
  return requestClient.get(`/rgdc-sys/dataTools/download/${download_url}`, {
    responseType: 'blob',
  });
}

/**
 * 图像查看接口
 * 返回Base64编码的图像数据
 * @param view_url 图像URL路径
 */
export async function viewImage(view_url: string) {
  return requestClient.get('/rgdc-sys/dataTools/view', {
    params: { view_url },
  });
}

/**
 * 数据对齐接口
 * 支持文本和图像JSON文件的对齐处理
 * @param data FormData containing text_json and image_json files
 */
export async function alignData(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/align', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 120_000,
  });
}

// 兼容性函数 - 保持向后兼容
/**
 * 创建汇聚任务 (兼容性函数)
 * @param data 任务数据
 */
export async function createTaskAll(data: FormData) {
  return extractData(data);
}

/**
 * 上传文件 (兼容性函数)
 * @param file 文件
 */
export async function uploadFile(file: FormData) {
  return extractData(file);
}

/**
 * 下载任务结果 (兼容性函数)
 * @param taskId 任务ID或下载URL
 */
export async function downloadTaskResult(taskId: string) {
  return downloadFile(taskId);
}
export async function getGatherTasks(params: {
  page: number;
  page_size: number;
}) {
  return requestClient.get('/rgdc-sys/gather_api/get_gather_tasks', {
    params,
  });
}
