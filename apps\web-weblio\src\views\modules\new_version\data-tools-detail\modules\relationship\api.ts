import { requestClient } from '#/api/request';

/**
 * 数据抽取接口
 * 支持文件上传和数据参数
 * @param data FormData containing files and data parameters
 */
export async function extractKnowledge(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/extract', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 120_000, // 增加超时时间以适应大文件处理
  });
}

/**
 * JSON文件下载接口
 * @param download_url 下载URL路径
 */
export async function downloadKnowledge(download_url: string) {
  return requestClient.get(`/rgdc-sys/dataTools/download/${download_url}`, {
    responseType: 'blob',
  });
}

/**
 * 图像查看接口
 * 返回Base64编码的图像数据
 * @param view_url 图像URL路径
 */
export async function viewKnowledge(view_url: string) {
  return requestClient.get('/rgdc-sys/dataTools/view', {
    params: {
      view_url,
    },
  });
}

/**
 * 数据对齐接口
 * 支持文本和图像JSON文件的对齐处理
 * @param data FormData containing text_json and image_json files
 */
export async function alignKnowledge(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/align', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 120_000, // 增加超时时间以适应对齐处理
  });
}
