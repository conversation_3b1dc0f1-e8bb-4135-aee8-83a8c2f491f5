import { CaretRightSmallIcon } from 'tdesign-icons-vue-next';
import { Breadcrumb } from 'tdesign-vue-next';
import { defineComponent } from 'vue';

import styles from './style.module.less';

export default defineComponent({
  name: 'Breadcrumb',
  setup(props, { attrs }) {
    return () => (
      <div class={styles.breadcrumbContainer}>
        <Breadcrumb {...attrs}>
          {{
            separator: () => (
              <CaretRightSmallIcon style="color: #fff !important" />
            ),
          }}
        </Breadcrumb>
      </div>
    );
  },
});
