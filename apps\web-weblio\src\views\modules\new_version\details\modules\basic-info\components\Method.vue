<template>
  <div class="page-header">
    <h1 class="page-title">Method Codes for Summary Calculations</h1>
    <button class="back-button" @click="goBack">
      返回
    </button>
  </div>

  <div class="method-codes-page">
    <div class="content-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在加载方法信息...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <p class="error-text">{{ error }}</p>
        <button class="retry-button" @click="fetchMethodData">重试</button>
      </div>

      <!-- 数据显示 -->
      <div v-else>
        <!-- 循环显示API返回的方法数据 -->
        <div v-for="(method, index) in methodList" :key="method.id || index" class="method-section">
          <div class="method-title" v-html="method.methodTitle"></div>
          <div class="description-text" v-html="method.methodDescribe || '暂无描述'">
          </div>
        </div>

        <!-- 如果没有数据 -->
        <div v-if="methodList.length === 0" class="no-data-container">
          <p class="no-data-text">暂无方法信息</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useSearchStore } from '#/store/search';
import { getMethodData } from '../api';

// 定义方法数据的类型接口
interface MethodItem {
  id?: string;
  methodTitle?: string;
  methodDescribe?: string;
  methodSymbol?: string;
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
   * 加载状态
   */
  loading: true,
});

const router = useRouter();
const searchStore = useSearchStore();

// 响应式数据，添加明确的类型定义
const methodList = ref<MethodItem[]>([]);
const loading = ref<boolean>(false);
const error = ref<string>('');

// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

// 获取方法数据
const fetchMethodData = async (): Promise<void> => {
  try {
    loading.value = true;
    error.value = '';

    // 调用API获取数据
    const response = await getMethodData({ inchikey: state.detailItem.baseCode });

    // 根据API返回的数据结构调整
    if (response) {
      methodList.value = response;
    } else {
      methodList.value = [];
    }
  } catch (err: unknown) {
    console.error('获取方法数据失败:', err);
    error.value = '获取方法数据失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 返回上一页
const goBack = (): void => {
  router.back();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchMethodData();
});
</script>

<style scoped>
.method-codes-page {
  min-height: 100vh;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.page-title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #40a9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.content-container {
  margin: 0 auto;
}

.method-section {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.method-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.method-title {
  width: 10%;
  color: #1890ff;
  font-size: 16px;
  font-weight: 600;
  display: inline-block;
}

.description-text {
  width: 90%;
  margin-left: auto;
  font-size: 16px;
  color: #333;
}

.method-details {
  margin-top: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.details-text {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.6;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid #ff4d4f;
}

.error-text {
  color: #ff4d4f;
  font-size: 16px;
  margin: 0 0 20px 0;
  text-align: center;
}

.retry-button {
  padding: 10px 20px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: #ff7875;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

/* 无数据状态样式 */
.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.no-data-text {
  color: #999;
  font-size: 16px;
  margin: 0;
}

.additional-info {
  background: linear-gradient(135deg, #1e5fae 0%, #2a69c4 100%);
  color: white;
  border-radius: 12px;
  padding: 30px;
  margin-top: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 15px 0;
  color: white;
}

.info-text {
  font-size: 16px;
  line-height: 1.7;
  margin: 0;
  opacity: 0.95;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .method-codes-page {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }

  .method-section {
    padding: 20px;
  }

  .method-title {
    font-size: 20px;
  }

  .additional-info {
    padding: 20px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.method-section {
  animation: fadeInUp 0.6s ease-out;
}

.method-section:nth-child(2) {
  animation-delay: 0.1s;
}

.method-section:nth-child(3) {
  animation-delay: 0.2s;
}

.additional-info {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.3s;
}
</style>
