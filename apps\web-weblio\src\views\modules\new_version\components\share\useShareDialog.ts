import { getUserInfoApi } from '#/api/core/user';
import { rsaEncrypt } from '#/utils/rsa';
import { h, ref } from 'vue';

import ShareDialogComponent from './ShareDialog';

/**
 * 分享弹窗 hooks
 * @returns openShareDialog, ShareDialog, getShareUrl
 */
export function useShareDialog() {
  const visible = ref(false);
  const shareUrl = ref('');

  // 获取分享链接（不区分类型）
  async function getShareUrl(baseCode: string, _fileType?: string) {
    const rsa_baseCode = rsaEncrypt(baseCode).replaceAll('+', '%252B');
    const user_info = await getUserInfoApi();
    const rsa_username = rsaEncrypt(user_info.username).replaceAll(
      '+',
      '%252B',
    );
    return `${import.meta.env.VITE_FRONT_URL}/datashare?p=${rsa_baseCode}&q=${rsa_username}`;
  }

  // 打开弹窗并设置分享链接
  function openShareDialog(url: string) {
    shareUrl.value = url;
    visible.value = true;
  }

  const ShareDialog = () =>
    h(ShareDialogComponent, {
      visible: visible.value,
      shareUrl: shareUrl.value,
      'onUpdate:visible': (val: boolean) => {
        visible.value = val;
      },
    });

  return {
    openShareDialog,
    ShareDialog,
    getShareUrl,
  };
}
