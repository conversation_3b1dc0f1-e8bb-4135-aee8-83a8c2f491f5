import type { PropType } from 'vue';

import { ShareIcon } from 'tdesign-icons-vue-next';
import { Button } from 'tdesign-vue-next';
import { defineComponent, unref, withModifiers } from 'vue';

import { useShareDialog } from './useShareDialog';

import styles from './style.module.less';

export default defineComponent({
  name: 'Share',
  props: {
    row: {
      type: Object as PropType<any>,
      required: true,
    },
  },
  setup(props) {
    const { getShareUrl, openShareDialog, ShareDialog } = useShareDialog();
    async function handleShare() {
      const url = await getShareUrl(unref(props?.row)?.baseCode);
      openShareDialog(url);
    }

    return () => (
      <>
        <Button
          class={styles.shareBtn}
          onClick={withModifiers(handleShare, ['stop'])}
          shape="circle"
          theme="default"
          variant="text"
        >
          <ShareIcon size="20" />
        </Button>
        <ShareDialog />
      </>
    );
  },
});
