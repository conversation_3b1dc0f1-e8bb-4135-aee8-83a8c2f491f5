<script setup lang="ts">
import {
  HeartIcon,
  ShareIcon,
  ViewListIcon,
  ViewModuleIcon,
  CalculatorIcon,
  ChevronLeftIcon
} from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  Loading,
  Pagination,
  Select,
  Table,
  Tag,
  Dialog
} from 'tdesign-vue-next';
import { computed, h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import {
  getGasPhaseInfo,
  getGasConstantPressureInfo,
  getGpHeatCapacityShomateEquationInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface GasPhaseInfo {
  quantity: string;
  value: string;
  units: string;
  method: string;
  reference: string;
  comment: string;
}

interface GasConstantInfo {
  constantPressure: string;
  temperature: string;
  reference: string;
  comment: string;
}

interface GpHeatInfo {
  formula: string;
  temperature: string;
  paramA: string;
  paramB: string;
  paramC: string;
  paramD: string;
  paramE: string;
  paramF: string;
  paramG: string;
  paramH: string;
  reference: string;
  comment: string;
}
/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});
// 响应式数据
const gasList = ref<GasPhaseInfo[][]>([]);
const gasConstantList = ref<GasConstantInfo[][]>([]);
const gpHeatList = ref<GpHeatInfo[]>([]);
// 跳转到References组件的函数
const navigateToReferences = () => {
  // 通过点击菜单项来切换到References标签页
  setTimeout(() => {
    // 查找包含"引用"文本的菜单项
    const referenceMenuItem = Array.from(document.querySelectorAll('[class*="menu-item-title"]')).find(el =>
      el.textContent?.includes('引用')
    ) || Array.from(document.querySelectorAll('[class*="menu-item"]')).find(el =>
      el.textContent?.includes('引用')
    );

    if (referenceMenuItem) {
      // 如果找到了引用菜单项，模拟点击
      (referenceMenuItem as HTMLElement).click();
    }
  }, 100);
};
// 表格列定义
const getGasColumns = () => [
  {
    colKey: 'quantity',
    title: 'Quantity',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.quantity })
  },
  {
    colKey: 'value',
    title: 'Value',
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.value })
  },
  {
    colKey: 'units',
    title: 'Units',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.units })
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => {
      return h('a', {
        innerHTML: row.method,
        style: 'color: #1890ff; cursor: pointer; text-decoration: underline;',
        onClick: (e) => {
          e.preventDefault();
          showMethodCodes();
        }
      });
    }
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('a', { 
      innerHTML: row.reference,
      style: 'color: #1890ff; cursor: pointer; text-decoration: underline;',
      onClick: (e) => {
        e.preventDefault();
        navigateToReferences();
      }
    })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 400,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  },
];

const getGasConstantColumns = () => [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'constantPressure',
    title: (h) => h('div', {
      style: 'display: grid; gap: 2px;'
    }, [
      h('div', ['C', h('sub', 'p,gas')]),
      h('div', '(J/mol·K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.constantPressure })
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', {
      style: 'display: grid; gap: 2px;'
    }, [
      h('div', 'Temperature'),
      h('div', '(K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('a', { 
      innerHTML: row.reference,
      style: 'color: #1890ff; cursor: pointer; text-decoration: underline;',
      onClick: (e) => {
        e.preventDefault();
        navigateToReferences();
      }
    })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getShomateColumns = () => [
  {
    colKey: 'temperature',
    title: 'Temperature (K)',
    width: 120,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'paramA',
    title: 'A',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.paramA })
  },
  {
    colKey: 'paramB',
    title: 'B',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.paramB })
  },
  {
    colKey: 'paramC',
    title: 'C',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.paramC })
  },
  {
    colKey: 'paramD',
    title: 'D',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.paramD })
  },
  {
    colKey: 'paramE',
    title: 'E',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.paramE })
  },
  {
    colKey: 'paramF',
    title: 'F',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.paramF })
  },
  {
    colKey: 'paramG',
    title: 'G',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.paramG })
  },
  {
    colKey: 'paramH',
    title: 'H',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.paramH })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 120,
    ellipsis: true,
    cell: (h, { row }) => h('a', { 
      innerHTML: row.reference,
      style: 'color: #1890ff; cursor: pointer; text-decoration: underline;',
      onClick: (e) => {
        e.preventDefault();
        navigateToReferences();
      }
    })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 220,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  },
];

// Store
const searchStore = useSearchStore();
const router = useRouter();

// 显示方法代码说明的函数
const showMethodCodes = () => {
  // 使用路由跳转到Method页面
  router.push({ name: 'MethodCodes' });
};
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const displayColumns = ref(['constantPressure', 'temperature', 'reference', 'comment']);
const getGasConstantRowspanAndColspan = ({ col, rowIndex }) => {
  if (col.colKey === 'reference' || col.colKey === 'comment') {
    if (rowIndex > 0) {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
    return { rowspan: gasConstantList.value.length, colspan: 1 }
  }
  return { rowspan: 1, colspan: 1 };
}
onMounted(async () => {
  try {
    // 调用气相信息查询API
    const response = await getGasPhaseInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    gasList.value = response;
    // 调用气相相变液体恒压热容信息查询API
    const res = await getGasConstantPressureInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    gasConstantList.value = res;
    const resData = await getGpHeatCapacityShomateEquationInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    gpHeatList.value = resData;
  } catch (error) {
    console.error('获取气相信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
});
</script>

<template>
  <div class="gas-phase-info">
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载气相信息中...</div>
    </div>
    <template v-else>
      <div v-if="gasList && gasList.length > 0">
        <h2>Gas phase thermochemistry data</h2>
        <div v-for="gas in gasList">
          <Table :data="gas" :columns="getGasColumns()" style="border:1px solid #e6e8eb" :bordered="true" resizable
            :hover="true" :stripe="true" row-key="quantity" table-layout="fixed" cell-empty-content="-" />
        </div>
      </div>
      <div v-if="gasConstantList && gasConstantList.length > 0">
        <br />
        <h2>Constant pressure heat capacity of gas</h2>
        <div v-for="gasConstant in gasConstantList">
          <Table :data="gasConstant" :columns="getGasConstantColumns()" :displayColumns="displayColumns"
            style="border:1px solid #e6e8eb" :bordered="true" resizable :hover="true" :stripe="true" row-key="id"
            :rowspan-and-colspan="getGasConstantRowspanAndColspan" table-layout="fixed" cell-empty-content="-" />
        </div>
      </div>
      <div v-if="gpHeatList && gpHeatList.length > 0">
        <br />
        <h2>Gas Phase Heat Capacity (Shomate Equation)</h2>
        <div v-if="gpHeatList[0]">
          <div v-html="gpHeatList[0].formula" style="padding: 10px;" />
          <Table :data="gpHeatList" :columns="getShomateColumns()" style="border:1px solid #e6e8eb" :bordered="true"
            resizable :hover="true" :stripe="true" row-key="temperature" table-layout="fixed" cell-empty-content="-" />
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.gas-phase-info {

  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-spinner {
      font-size: 16px;
      color: #666;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #e8e8e8;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    @keyframes spin {
      0% {
        transform: translateY(-50%) rotate(0deg);
      }

      100% {
        transform: translateY(-50%) rotate(360deg);
      }
    }
  }

  h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  :deep(.t-table td),
  :deep(.t-table th) {
    border-right: 1px solid #e6e8eb !important;
  }

  :deep(.t-table td:last-child),
  :deep(.t-table th:last-child) {
    border-right: none !important;
  }

  :deep(.t-table td) {
    max-height: 2.8em !important;
    line-height: 1.4 !important;
    padding: 8px !important;
    overflow: hidden !important;
    word-break: break-word !important;
  }
}
</style>
