<template>
  <div class="demo-container">
    <h2>Home Banner 组件演示</h2>
    <HomeBanner />
    
    <div class="description">
      <h3>功能特性：</h3>
      <ul>
        <li>✅ Canvas 绘制的交互式横幅</li>
        <li>✅ 鼠标悬停时的手风琴动画效果</li>
        <li>✅ 3D 环状箭头指针（带扭转效果）</li>
        <li>✅ 左右导航箭头支持滚动</li>
        <li>✅ 响应式设计</li>
        <li>✅ 节点包含图片、主标题、副标题</li>
        <li>✅ 点击节点可触发事件</li>
      </ul>
      
      <h3>技术实现：</h3>
      <ul>
        <li>Vue3 + TSX</li>
        <li>Canvas 2D 渲染</li>
        <li>CSS Modules 样式</li>
        <li>TypeScript 类型安全</li>
        <li>响应式状态管理</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import HomeBanner from './index';
</script>

<style scoped>
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.description {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.description h3 {
  color: #333;
  margin-bottom: 10px;
}

.description ul {
  margin: 0;
  padding-left: 20px;
}

.description li {
  margin-bottom: 5px;
  color: #666;
}
</style>
