import type { ListItem } from './usePermissions'; // 复用类型

import { ref } from 'vue';

// 1. 在 hook 外部创建单例状态，确保全局唯一
const isVisible = ref(false);
const currentItem = ref<ListItem | null>(null);
const onSuccessCallback = ref<(() => void) | null>(null);

export function useApprovalModal() {
  // 2. 定义打开弹窗的函数
  const openModal = (item: ListItem, onSuccess?: () => void) => {
    // 每次打开都更新为当前项的数据和回调，保证数据精准
    currentItem.value = item;
    onSuccessCallback.value = onSuccess || null;
    isVisible.value = true;
  };

  // 3. 定义关闭弹窗的函数
  const closeModal = () => {
    isVisible.value = false;
    // 关闭时清理数据，避免内存占用
    currentItem.value = null;
    onSuccessCallback.value = null;
  };

  // 4. 暴露状态和方法
  return {
    isVisible,
    currentItem,
    onSuccessCallback,
    openModal,
    closeModal,
  };
}
