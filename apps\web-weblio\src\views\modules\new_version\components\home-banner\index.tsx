import { ChevronLeft, ChevronRight } from '@vben-core/icons';
import { VbenButton } from '@vben-core/shadcn-ui';
import { computed, defineComponent, ref } from 'vue';

import styles from './style.module.less';

// 模拟节点数据接口
interface BannerNode {
  id: string;
  image: string;
  title: string;
  subtitle: string;
  description: string;
}

// 模拟数据
const mockData: BannerNode[] = [
  {
    id: '1',
    image: '/api/placeholder/120/80',
    title: '文献数据',
    subtitle: '论文文献的管理',
    description: '反应知识\n利用领域知识进行数据',
  },
  {
    id: '2',
    image: '/api/placeholder/120/80',
    title: '热力学数据',
    subtitle: '实验小试的研究数据',
    description: '热力学数据\n实验小试的研究数据',
  },
  {
    id: '3',
    image: '/api/placeholder/120/80',
    title: '中试数据',
    subtitle: '工厂中试台的数据',
    description: '中试数据\n工厂中试台的数据',
  },
  {
    id: '4',
    image: '/api/placeholder/120/80',
    title: '生产工艺数据',
    subtitle: '检测生产设备的工艺',
    description: '生产工艺数据\n检测生产设备的工艺',
  },
  {
    id: '5',
    image: '/api/placeholder/120/80',
    title: '产业知识',
    subtitle: '产业相关知识',
    description: '产业知识\n产业相关知识',
  },
];

export default defineComponent({
  name: 'HomeBanner',
  setup() {
    const canvasRef = ref<HTMLCanvasElement>();
    const containerRef = ref<HTMLDivElement>();
    const hoveredNodeId = ref<null | string>(null);
    const currentOffset = ref(0);
    const animationId = ref<number>();
    const isAnimating = ref(false);

    // 节点配置
    const nodeConfig = {
      width: 200,
      height: 150,
      spacing: 20,
      hoverWidth: 280,
      animationDuration: 300,
    };

    // 计算容器宽度和可见节点数量
    const containerWidth = ref(1200);
    const visibleNodes = computed(() => {
      const totalWidth = containerWidth.value - 100; // 减去左右箭头空间
      return Math.floor(totalWidth / (nodeConfig.width + nodeConfig.spacing));
    });

    // 计算最大偏移量
    const maxOffset = computed(() => {
      const totalNodes = mockData.length;
      const maxVisibleNodes = visibleNodes.value;
      return Math.max(0, totalNodes - maxVisibleNodes);
    });

    let ctx: CanvasRenderingContext2D | null = null;
    const images: Map<string, HTMLImageElement> = new Map();

    // 初始化Canvas
    const initCanvas = () => {
      if (!canvasRef.value || !containerRef.value) return;

      const canvas = canvasRef.value;
      const container = containerRef.value;

      // 设置Canvas尺寸
      const rect = container.getBoundingClientRect();
      containerWidth.value = rect.width;
      canvas.width = rect.width;
      canvas.height = 300;

      ctx = canvas.getContext('2d');
      if (!ctx) return;

      // 设置Canvas样式
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // 预加载图片
      loadImages();
    };

    // 预加载图片
    const loadImages = async () => {
      const imagePromises = mockData.map((node) => {
        return new Promise<void>((resolve) => {
          const img = new Image();
          img.crossOrigin = 'anonymous';
          img.addEventListener('load', () => {
            images.set(node.id, img);
            resolve();
          });
          img.onerror = () => {
            // 创建占位图片
            const placeholderImg = createPlaceholderImage(120, 80);
            images.set(node.id, placeholderImg);
            resolve();
          };
          img.src = node.image;
        });
      });

      await Promise.all(imagePromises);
      draw();
    };

    // 创建占位图片
    const createPlaceholderImage = (
      width: number,
      height: number,
    ): HTMLImageElement => {
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d')!;

      // 绘制占位图片
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, width, height);
      ctx.fillStyle = '#ccc';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('图片', width / 2, height / 2);

      const img = new Image();
      img.src = canvas.toDataURL();
      return img;
    };

    return () => (
      <div class={styles.container} ref={containerRef}>
        <div class={styles.bannerWrapper}>
          {/* 左箭头 */}
          <VbenButton
            class={styles.navButton}
            disabled={currentOffset.value <= 0}
            onClick={() => scrollNodes('left')}
            size="icon"
            variant="outline"
          >
            <ChevronLeft />
          </VbenButton>

          {/* Canvas容器 */}
          <div class={styles.canvasContainer}>
            <canvas
              class={styles.canvas}
              onClick={handleCanvasClick}
              onMouseleave={handleMouseLeave}
              onMousemove={handleMouseMove}
              ref={canvasRef}
            />
          </div>

          {/* 右箭头 */}
          <VbenButton
            class={styles.navButton}
            disabled={currentOffset.value >= maxOffset.value}
            onClick={() => scrollNodes('right')}
            size="icon"
            variant="outline"
          >
            <ChevronRight />
          </VbenButton>
        </div>
      </div>
    );
  },
});
