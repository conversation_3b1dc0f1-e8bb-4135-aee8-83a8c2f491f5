import { ChevronLeft, ChevronRight } from '@vben/icons';
import {
  computed,
  defineComponent,
  onMounted,
  onUnmounted,
  ref,
  watch,
} from 'vue';

import styles from './style.module.less';

// 模拟节点数据接口
interface BannerNode {
  id: string;
  image: string;
  title: string;
  subtitle: string;
  description: string;
}

// 模拟数据
const mockData: BannerNode[] = [
  {
    id: '1',
    image: '/api/placeholder/120/80',
    title: '文献数据',
    subtitle: '论文文献的管理',
    description: '反应知识\n利用领域知识进行数据',
  },
  {
    id: '2',
    image: '/api/placeholder/120/80',
    title: '热力学数据',
    subtitle: '实验小试的研究数据',
    description: '热力学数据\n实验小试的研究数据',
  },
  {
    id: '3',
    image: '/api/placeholder/120/80',
    title: '中试数据',
    subtitle: '工厂中试台的数据',
    description: '中试数据\n工厂中试台的数据',
  },
  {
    id: '4',
    image: '/api/placeholder/120/80',
    title: '生产工艺数据',
    subtitle: '检测生产设备的工艺',
    description: '生产工艺数据\n检测生产设备的工艺',
  },
  {
    id: '5',
    image: '/api/placeholder/120/80',
    title: '产业知识',
    subtitle: '产业相关知识',
    description: '产业知识\n产业相关知识',
  },
];

export default defineComponent({
  name: 'HomeBanner',
  setup() {
    const canvasRef = ref<HTMLCanvasElement>();
    const containerRef = ref<HTMLDivElement>();
    const hoveredNodeId = ref<null | string>(null);
    const currentOffset = ref(0);
    const animationId = ref<number>();

    // 节点配置
    const nodeConfig = {
      width: 200,
      height: 150,
      spacing: 20,
      hoverWidth: 280,
      animationDuration: 300,
    };

    // 计算容器宽度和可见节点数量
    const containerWidth = ref(1200);
    const visibleNodes = computed(() => {
      const totalWidth = containerWidth.value - 100; // 减去左右箭头空间
      return Math.floor(totalWidth / (nodeConfig.width + nodeConfig.spacing));
    });

    // 计算最大偏移量
    const maxOffset = computed(() => {
      const totalNodes = mockData.length;
      const maxVisibleNodes = visibleNodes.value;
      return Math.max(0, totalNodes - maxVisibleNodes);
    });

    let ctx: CanvasRenderingContext2D | null = null;
    const images: Map<string, HTMLImageElement> = new Map();

    // 创建占位图片
    const createPlaceholderImage = (
      width: number,
      height: number,
    ): HTMLImageElement => {
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      if (!ctx) return new Image();

      // 绘制占位图片
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, width, height);
      ctx.fillStyle = '#ccc';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('图片', width / 2, height / 2);

      const img = new Image();
      img.src = canvas.toDataURL();
      return img;
    };

    // 绘制函数
    const draw = () => {
      if (!ctx || !canvasRef.value) return;

      const canvas = canvasRef.value;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 计算节点位置
      const startX = 50;
      const centerY = canvas.height / 2;

      // 绘制可见的节点
      const visibleNodeCount = visibleNodes.value;
      const startIndex = currentOffset.value;
      const endIndex = Math.min(startIndex + visibleNodeCount, mockData.length);

      for (let i = startIndex; i < endIndex; i++) {
        const node = mockData[i];
        if (!node) continue;
        const nodeIndex = i - startIndex;
        const isHovered = hoveredNodeId.value === node.id;

        // 计算节点宽度（手风琴效果）
        const currentWidth = isHovered
          ? nodeConfig.hoverWidth
          : nodeConfig.width;
        const x = startX + nodeIndex * (nodeConfig.width + nodeConfig.spacing);

        drawNode(
          node,
          x,
          centerY - nodeConfig.height / 2,
          currentWidth,
          nodeConfig.height,
          isHovered,
        );
      }
    };

    // 绘制单个节点
    const drawNode = (
      node: BannerNode,
      x: number,
      y: number,
      width: number,
      height: number,
      isHovered: boolean,
    ) => {
      if (!ctx) return;

      // 绘制背景
      ctx.save();
      ctx.fillStyle = isHovered ? '#f0f8ff' : '#ffffff';
      ctx.strokeStyle = isHovered ? '#4a90e2' : '#e0e0e0';
      ctx.lineWidth = 2;

      // 绘制圆角矩形
      const radius = 12;
      ctx.beginPath();
      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height - radius);
      ctx.quadraticCurveTo(
        x + width,
        y + height,
        x + width - radius,
        y + height,
      );
      ctx.lineTo(x + radius, y + height);
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();

      // 绘制图片
      const img = images.get(node.id);
      if (img) {
        const imgWidth = 60;
        const imgHeight = 40;
        const imgX = x + (width - imgWidth) / 2;
        const imgY = y + 20;
        ctx.drawImage(img, imgX, imgY, imgWidth, imgHeight);
      }

      // 绘制标题
      ctx.fillStyle = '#333';
      ctx.font = 'bold 16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(node.title, x + width / 2, y + 80);

      // 绘制副标题
      ctx.fillStyle = '#666';
      ctx.font = '12px Arial';
      ctx.fillText(node.subtitle, x + width / 2, y + 100);

      // 绘制环状箭头
      drawCircularArrow(x + width / 2, y + height - 25, 15, isHovered);

      ctx.restore();
    };

    // 绘制3D环状箭头 - 重新设计
    const drawCircularArrow = (
      centerX: number,
      centerY: number,
      radius: number,
      isHovered: boolean,
    ) => {
      if (!ctx) return;

      ctx.save();

      // 定义颜色
      const frontColor = isHovered ? '#4a90e2' : '#5a9fd4';
      const backColor = isHovered ? '#2c5aa0' : '#3d7ba8';
      const highlightColor = isHovered ? '#6bb6ff' : '#7bb3e0';

      // 绘制更好看的3D环状箭头
      drawBetterCircularArrow(
        centerX,
        centerY,
        radius,
        frontColor,
        backColor,
        highlightColor,
      );

      ctx.restore();
    };

    // 绘制更好看的3D环状箭头
    const drawBetterCircularArrow = (
      centerX: number,
      centerY: number,
      radius: number,
      frontColor: string,
      backColor: string,
      highlightColor: string,
    ) => {
      if (!ctx) return;

      const thickness = 6;
      const startAngle = 0.3; // 起始角度
      const endAngle = Math.PI * 1.7; // 结束角度
      const arrowSize = 8;

      // 1. 绘制阴影层（底层）
      ctx.beginPath();
      ctx.arc(centerX + 2, centerY + 2, radius, startAngle, endAngle);
      ctx.lineWidth = thickness + 2;
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
      ctx.stroke();

      // 2. 绘制背面部分（深色）
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, startAngle + Math.PI * 0.5, endAngle);
      ctx.lineWidth = thickness;
      ctx.strokeStyle = backColor;
      ctx.stroke();

      // 3. 绘制正面部分（亮色）
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, startAngle, startAngle + Math.PI * 0.8);
      ctx.lineWidth = thickness;
      ctx.strokeStyle = frontColor;
      ctx.stroke();

      // 4. 绘制高光
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, startAngle, startAngle + Math.PI * 0.4);
      ctx.lineWidth = thickness - 2;
      ctx.strokeStyle = highlightColor;
      ctx.stroke();

      // 5. 绘制箭头头部
      const arrowAngle = endAngle;
      const arrowX = centerX + radius * Math.cos(arrowAngle);
      const arrowY = centerY + radius * Math.sin(arrowAngle);

      // 计算箭头方向
      const tangentAngle = arrowAngle + Math.PI / 2;

      // 箭头阴影
      ctx.beginPath();
      ctx.moveTo(arrowX + 2, arrowY + 2);
      ctx.lineTo(
        arrowX + arrowSize * Math.cos(tangentAngle) + 2,
        arrowY + arrowSize * Math.sin(tangentAngle) + 2,
      );
      ctx.lineTo(
        arrowX + arrowSize * 0.5 * Math.cos(tangentAngle - Math.PI / 3) + 2,
        arrowY + arrowSize * 0.5 * Math.sin(tangentAngle - Math.PI / 3) + 2,
      );
      ctx.lineTo(
        arrowX + arrowSize * 0.5 * Math.cos(tangentAngle + Math.PI / 3) + 2,
        arrowY + arrowSize * 0.5 * Math.sin(tangentAngle + Math.PI / 3) + 2,
      );
      ctx.closePath();
      ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
      ctx.fill();

      // 箭头主体
      ctx.beginPath();
      ctx.moveTo(arrowX, arrowY);
      ctx.lineTo(
        arrowX + arrowSize * Math.cos(tangentAngle),
        arrowY + arrowSize * Math.sin(tangentAngle),
      );
      ctx.lineTo(
        arrowX + arrowSize * 0.5 * Math.cos(tangentAngle - Math.PI / 3),
        arrowY + arrowSize * 0.5 * Math.sin(tangentAngle - Math.PI / 3),
      );
      ctx.lineTo(
        arrowX + arrowSize * 0.5 * Math.cos(tangentAngle + Math.PI / 3),
        arrowY + arrowSize * 0.5 * Math.sin(tangentAngle + Math.PI / 3),
      );
      ctx.closePath();
      ctx.fillStyle = frontColor;
      ctx.fill();

      // 箭头高光
      ctx.beginPath();
      ctx.moveTo(arrowX, arrowY);
      ctx.lineTo(
        arrowX + arrowSize * 0.7 * Math.cos(tangentAngle),
        arrowY + arrowSize * 0.7 * Math.sin(tangentAngle),
      );
      ctx.strokeStyle = highlightColor;
      ctx.lineWidth = 2;
      ctx.stroke();
    };

    // 绘制环形箭头主体
    const drawArrowRing = (
      centerX: number,
      centerY: number,
      radius: number,
      thickness: number,
      primaryColor: string,
      secondaryColor: string,
      shadowColor: string,
    ) => {
      if (!ctx) return;

      // 绘制外圈（阴影效果）
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius + thickness / 2, 0.2, Math.PI * 1.6);
      ctx.lineWidth = thickness + 2;
      ctx.strokeStyle = shadowColor;
      ctx.stroke();

      // 绘制主体环形
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0.2, Math.PI * 1.6);
      ctx.lineWidth = thickness;

      // 创建渐变效果模拟3D
      const gradient = ctx.createLinearGradient(
        centerX - radius,
        centerY - radius,
        centerX + radius,
        centerY + radius,
      );
      gradient.addColorStop(0, primaryColor);
      gradient.addColorStop(0.5, secondaryColor);
      gradient.addColorStop(1, primaryColor);

      ctx.strokeStyle = gradient;
      ctx.stroke();

      // 绘制高光效果
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0.2, Math.PI * 0.8);
      ctx.lineWidth = thickness - 2;
      ctx.strokeStyle = secondaryColor;
      ctx.stroke();
    };

    // 绘制箭头头部
    const drawArrowHead = (
      centerX: number,
      centerY: number,
      radius: number,
      arrowLength: number,
      arrowWidth: number,
      primaryColor: string,
      secondaryColor: string,
      shadowColor: string,
    ) => {
      if (!ctx) return;

      // 计算箭头位置（在环形末端）
      const angle = Math.PI * 1.6;
      const arrowX = centerX + radius * Math.cos(angle);
      const arrowY = centerY + radius * Math.sin(angle);

      // 计算箭头方向
      const tangentAngle = angle + Math.PI / 2;

      // 箭头的三个顶点
      const tipX = arrowX + arrowLength * Math.cos(tangentAngle);
      const tipY = arrowY + arrowLength * Math.sin(tangentAngle);

      const base1X =
        arrowX - (arrowWidth / 2) * Math.cos(tangentAngle - Math.PI / 2);
      const base1Y =
        arrowY - (arrowWidth / 2) * Math.sin(tangentAngle - Math.PI / 2);

      const base2X =
        arrowX + (arrowWidth / 2) * Math.cos(tangentAngle - Math.PI / 2);
      const base2Y =
        arrowY + (arrowWidth / 2) * Math.sin(tangentAngle - Math.PI / 2);

      // 绘制箭头阴影
      ctx.beginPath();
      ctx.moveTo(tipX + 2, tipY + 2);
      ctx.lineTo(base1X + 2, base1Y + 2);
      ctx.lineTo(base2X + 2, base2Y + 2);
      ctx.closePath();
      ctx.fillStyle = shadowColor;
      ctx.fill();

      // 绘制箭头主体
      ctx.beginPath();
      ctx.moveTo(tipX, tipY);
      ctx.lineTo(base1X, base1Y);
      ctx.lineTo(base2X, base2Y);
      ctx.closePath();

      // 创建箭头渐变
      const arrowGradient = ctx.createLinearGradient(
        base1X,
        base1Y,
        tipX,
        tipY,
      );
      arrowGradient.addColorStop(0, primaryColor);
      arrowGradient.addColorStop(1, secondaryColor);

      ctx.fillStyle = arrowGradient;
      ctx.fill();

      // 绘制箭头边框
      ctx.strokeStyle = shadowColor;
      ctx.lineWidth = 1;
      ctx.stroke();

      // 绘制箭头高光
      ctx.beginPath();
      ctx.moveTo(tipX, tipY);
      ctx.lineTo((base1X + tipX) / 2, (base1Y + tipY) / 2);
      ctx.strokeStyle = secondaryColor;
      ctx.lineWidth = 2;
      ctx.stroke();
    };

    // 初始化Canvas
    const initCanvas = () => {
      if (!canvasRef.value || !containerRef.value) return;

      const canvas = canvasRef.value;
      const container = containerRef.value;

      // 设置Canvas尺寸
      const rect = container.getBoundingClientRect();
      containerWidth.value = rect.width;
      canvas.width = rect.width;
      canvas.height = 300;

      ctx = canvas.getContext('2d');
      if (!ctx) return;

      // 设置Canvas样式
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // 预加载图片
      loadImages();
    };

    // 预加载图片
    const loadImages = async () => {
      const imagePromises = mockData.map((node) => {
        return new Promise<void>((resolve) => {
          const img = new Image();
          img.crossOrigin = 'anonymous';
          img.addEventListener('load', () => {
            images.set(node.id, img);
            resolve();
          });
          img.addEventListener('error', () => {
            // 创建占位图片
            const placeholderImg = createPlaceholderImage(120, 80);
            images.set(node.id, placeholderImg);
            resolve();
          });
          img.src = node.image;
        });
      });

      await Promise.all(imagePromises);
      draw();
    };

    // 鼠标移动事件处理
    const handleMouseMove = (event: MouseEvent) => {
      if (!canvasRef.value) return;

      const rect = canvasRef.value.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      // 检测鼠标是否在节点上
      const startX = 50;
      const centerY = canvasRef.value.height / 2;
      const nodeY = centerY - nodeConfig.height / 2;

      let hoveredNode: null | string = null;

      const visibleNodeCount = visibleNodes.value;
      const startIndex = currentOffset.value;
      const endIndex = Math.min(startIndex + visibleNodeCount, mockData.length);

      for (let i = startIndex; i < endIndex; i++) {
        const node = mockData[i];
        if (!node) continue;

        const nodeIndex = i - startIndex;
        const x = startX + nodeIndex * (nodeConfig.width + nodeConfig.spacing);

        if (
          mouseX >= x &&
          mouseX <= x + nodeConfig.width &&
          mouseY >= nodeY &&
          mouseY <= nodeY + nodeConfig.height
        ) {
          hoveredNode = node.id;
          break;
        }
      }

      if (hoveredNodeId.value !== hoveredNode) {
        hoveredNodeId.value = hoveredNode;
        draw();
      }
    };

    // 鼠标离开事件处理
    const handleMouseLeave = () => {
      if (hoveredNodeId.value !== null) {
        hoveredNodeId.value = null;
        draw();
      }
    };

    // 画布点击事件处理
    const handleCanvasClick = (event: MouseEvent) => {
      if (!canvasRef.value) return;

      const rect = canvasRef.value.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      // 检测点击的节点
      const startX = 50;
      const centerY = canvasRef.value.height / 2;
      const nodeY = centerY - nodeConfig.height / 2;

      const visibleNodeCount = visibleNodes.value;
      const startIndex = currentOffset.value;
      const endIndex = Math.min(startIndex + visibleNodeCount, mockData.length);

      for (let i = startIndex; i < endIndex; i++) {
        const node = mockData[i];
        if (!node) continue;

        const nodeIndex = i - startIndex;
        const x = startX + nodeIndex * (nodeConfig.width + nodeConfig.spacing);

        if (
          mouseX >= x &&
          mouseX <= x + nodeConfig.width &&
          mouseY >= nodeY &&
          mouseY <= nodeY + nodeConfig.height
        ) {
          // 这里可以添加节点点击的处理逻辑
          // 例如：跳转到详情页面或触发其他事件
          break;
        }
      }
    };

    // 滚动节点
    const scrollNodes = (direction: 'left' | 'right') => {
      if (direction === 'left' && currentOffset.value > 0) {
        currentOffset.value--;
      } else if (
        direction === 'right' &&
        currentOffset.value < maxOffset.value
      ) {
        currentOffset.value++;
      }
      draw();
    };

    // 窗口大小变化处理
    const handleResize = () => {
      initCanvas();
    };

    // 生命周期
    onMounted(() => {
      initCanvas();
      window.addEventListener('resize', handleResize);
    });

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize);
      if (animationId.value) {
        cancelAnimationFrame(animationId.value);
      }
    });

    // 监听偏移量变化
    watch(currentOffset, () => {
      draw();
    });

    return () => (
      <div class={styles.container} ref={containerRef}>
        <div class={styles.bannerWrapper}>
          {/* 左箭头 */}
          <button
            class={styles.navButton}
            disabled={currentOffset.value <= 0}
            onClick={() => scrollNodes('left')}
          >
            <ChevronLeft />
          </button>

          {/* Canvas容器 */}
          <div class={styles.canvasContainer}>
            <canvas
              class={styles.canvas}
              onClick={handleCanvasClick}
              onMouseleave={handleMouseLeave}
              onMousemove={handleMouseMove}
              ref={canvasRef}
            />
          </div>

          {/* 右箭头 */}
          <button
            class={styles.navButton}
            disabled={currentOffset.value >= maxOffset.value}
            onClick={() => scrollNodes('right')}
          >
            <ChevronRight />
          </button>
        </div>
      </div>
    );
  },
});
