<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { getDSubstanceBusinessInfoListByInchikey, getDSubstanceBusinessInfoDetailsListByPage } from '../api';
import { getDictItems } from '#/api';
import { useSearchStore } from '#/store/search';
import { computed, reactive } from 'vue';
import { MessagePlugin, Table, Card, Space, Tooltip } from 'tdesign-vue-next';
import type { PageInfo } from 'tdesign-vue-next';
import { Pagination } from 'tdesign-vue-next';
import {
  ShopIcon,
  InternetIcon,
  LocationIcon,
  CallIcon
} from 'tdesign-icons-vue-next';

/**
 * 分页参数
 */
const Paginations = {
  current: 1,
  pageSize: 20,
  total: 0,
};
const pagination: any = ref(Paginations);

// 商家信息接口
interface BusinessInfo {
  id: number;
  createdBy?: string;
  createTime?: string;
  updatedBy?: string;
  updateTime?: string;
  isDeleted?: string;
  inchikey?: string;
  companyName?: string;
  webUrl?: string;
  address?: string;
  contactNumber?: string;
  products?: string;
  infoSources?: string;
  [key: string]: any;
}

// 商家详细产品信息接口
interface BusinessDetailItem {
  id: number;
  createdBy?: string;
  createTime?: string;
  updatedBy?: string;
  updateTime?: string;
  isDeleted?: string;
  inchikey?: string;
  biId?: number;
  cas?: string;
  brand?: string;
  itemNumber?: string;
  packagingUnit?: string;
  deliveryTime?: string;
  productNameC?: string;
  productNameE?: string;
  qualityStandard?: string;
  delPrice?: number;
  price?: number;
  calculatePrice?: number;
  source?: string;
  [key: string]: any;
}

// 商家表格数据接口
interface BusinessTableData {
  businessInfo: BusinessInfo;
  detailList: BusinessDetailItem[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  loading: boolean;
}

// 响应式数据
const businessList = ref<BusinessInfo[]>([]);
const businessTables = ref<Map<number, BusinessTableData>>(new Map());

// 字典数据
const dictData = ref<{
  specification: Array<{ text: string, value: string }>;
  purity: Array<{ text: string, value: string }>;
  price: Array<{ text: string, value: string }>;
  availability: Array<{ text: string, value: string }>;
  brand: Array<{ text: string, value: string }>;
}>({
  specification: [],
  purity: [],
  price: [],
  availability: [],
  brand: []
});

// 字段英文名到中文显示的映射
const filterLabels: Record<string, string> = {
  specification: '规格',
  purity: '纯度',
  price: '价格',
  availability: '是否现货',
  brand: '品牌'
};

// 当前选中的筛选条件
const selectedFilters = ref<{
  specification: string;
  purity: string;
  price: string;
  availability: string;
  brand: string;
}>({
  specification: '',
  purity: '',
  price: '',
  availability: '',
  brand: ''
});

// 自定义价格区间
const customPriceRange = ref({
  min: '',
  max: ''
});

// 解析百分比条件为最小值最大值
const parsePercentageCondition = (condition: string | undefined) => {
  if (!condition || typeof condition !== 'string') return {
    min: null,
    max: null,
    minOperator: null,
    maxOperator: null
  };

  // 匹配 >98%, >=98%, <98%, <=98%, 95%-98%, 等格式（修复支持两端都有%的范围）
  const safeCondition = condition as string;
  const greaterThanMatch = safeCondition.match(/^>(\d+(?:\.\d+)?)%?$/);
  const greaterEqualMatch = safeCondition.match(/^>=(\d+(?:\.\d+)?)%?$/);
  const lessThanMatch = safeCondition.match(/^<(\d+(?:\.\d+)?)%?$/);
  const lessEqualMatch = safeCondition.match(/^<=(\d+(?:\.\d+)?)%?$/);
  const rangeMatch = safeCondition.match(/^(\d+(?:\.\d+)?)%?\s*-\s*(\d+(?:\.\d+)?)%?$/);
  const exactMatch = safeCondition.match(/^(\d+(?:\.\d+)?)%?$/);

  if (greaterThanMatch && greaterThanMatch[1]) {
    return {
      min: parseFloat(greaterThanMatch[1]),
      max: null,
      minOperator: '>',
      maxOperator: null
    };
  } else if (greaterEqualMatch && greaterEqualMatch[1]) {
    return {
      min: parseFloat(greaterEqualMatch[1]),
      max: null,
      minOperator: '>=',
      maxOperator: null
    };
  } else if (lessThanMatch && lessThanMatch[1]) {
    return {
      min: null,
      max: parseFloat(lessThanMatch[1]),
      minOperator: null,
      maxOperator: '<'
    };
  } else if (lessEqualMatch && lessEqualMatch[1]) {
    return {
      min: null,
      max: parseFloat(lessEqualMatch[1]),
      minOperator: null,
      maxOperator: '<='
    };
  } else if (rangeMatch && rangeMatch[1] && rangeMatch[2]) {
    return {
      min: parseFloat(rangeMatch[1]),
      max: parseFloat(rangeMatch[2]),
      minOperator: '>=',
      maxOperator: '<='
    };
  } else if (exactMatch && exactMatch[1]) {
    const value = parseFloat(exactMatch[1]);
    return {
      min: value,
      max: value,
      minOperator: '>=',
      maxOperator: '<='
    };
  }

  return {
    min: null,
    max: null,
    minOperator: null,
    maxOperator: null
  };
};

// 解析价格条件为最小值最大值
const parsePriceCondition = (condition: string | undefined) => {
  if (!condition || typeof condition !== 'string') return {
    min: null,
    max: null,
    minOperator: null,
    maxOperator: null
  };

  // 匹配 >300, >=300, <300, <=300, 300-500, 等格式
  const safeCondition = condition as string;
  const greaterThanMatch = safeCondition.match(/^>(\d+(?:\.\d+)?)$/);
  const greaterEqualMatch = safeCondition.match(/^>=(\d+(?:\.\d+)?)$/);
  const lessThanMatch = safeCondition.match(/^<(\d+(?:\.\d+)?)$/);
  const lessEqualMatch = safeCondition.match(/^<=(\d+(?:\.\d+)?)$/);
  const rangeMatch = safeCondition.match(/^(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)$/);
  const exactMatch = safeCondition.match(/^(\d+(?:\.\d+)?)$/);

  if (greaterThanMatch && greaterThanMatch[1]) {
    return {
      min: parseFloat(greaterThanMatch[1]),
      max: null,
      minOperator: '>',
      maxOperator: null
    };
  } else if (greaterEqualMatch && greaterEqualMatch[1]) {
    return {
      min: parseFloat(greaterEqualMatch[1]),
      max: null,
      minOperator: '>=',
      maxOperator: null
    };
  } else if (lessThanMatch && lessThanMatch[1]) {
    return {
      min: null,
      max: parseFloat(lessThanMatch[1]),
      minOperator: null,
      maxOperator: '<'
    };
  } else if (lessEqualMatch && lessEqualMatch[1]) {
    return {
      min: null,
      max: parseFloat(lessEqualMatch[1]),
      minOperator: null,
      maxOperator: '<='
    };
  } else if (rangeMatch && rangeMatch[1] && rangeMatch[2]) {
    return {
      min: parseFloat(rangeMatch[1]),
      max: parseFloat(rangeMatch[2]),
      minOperator: '>=',
      maxOperator: '<='
    };
  } else if (exactMatch && exactMatch[1]) {
    const value = parseFloat(exactMatch[1]);
    return {
      min: value,
      max: value,
      minOperator: '>=',
      maxOperator: '<='
    };
  }

  return {
    min: null,
    max: null,
    minOperator: null,
    maxOperator: null
  };
};

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
   * 加载状态
   */
  loading: false,
  /**
   * 筛选参数（从选中的筛选条件计算得出）
   */
  filterParams: computed(() => {
    const params: any = {
      specification: selectedFilters.value.specification,
      availability: selectedFilters.value.availability,
      brand: selectedFilters.value.brand,
    };

    // 处理纯度条件
    if (selectedFilters.value.purity) {
      const purityRange = parsePercentageCondition(selectedFilters.value.purity);
      // 百分数转小数
      let purityMin = purityRange.min;
      let purityMax = purityRange.max;
      if (typeof purityMin === 'number' && purityMin > 1) purityMin = purityMin / 100;
      if (typeof purityMax === 'number' && purityMax > 1) purityMax = purityMax / 100;
      params.purityMin = purityMin;
      params.purityMax = purityMax;
      params.purityMinOperator = purityRange.minOperator;
      params.purityMaxOperator = purityRange.maxOperator;
    }

    // 处理价格条件（优先使用自定义价格）
    if (customPriceRange.value.min || customPriceRange.value.max) {
      params.priceMin = customPriceRange.value.min || null;
      params.priceMax = customPriceRange.value.max || null;
      // 自定义价格默认使用>=和<=操作符
      params.priceMinOperator = customPriceRange.value.min ? '>=' : null;
      params.priceMaxOperator = customPriceRange.value.max ? '<=' : null;
    } else if (selectedFilters.value.price) {
      const priceRange = parsePriceCondition(selectedFilters.value.price);
      params.priceMin = priceRange.min;
      params.priceMax = priceRange.max;
      params.priceMinOperator = priceRange.minOperator;
      params.priceMaxOperator = priceRange.maxOperator;
    }

    console.log('筛选参数处理结果:', params);
    return params;
  })
});

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  const detail = searchStore.currentDetailItem;
  if (detail) {
    console.log("当前详情数据", detail);
    return detail;
  } else {
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local);
    return JSON.parse(local ?? '{}');
  }
});

/**
 * 网络访问方法定义
 */
const reqRunner = {
  /**
   * 获取商家列表数据
   */
  getBusinessList: useRequest(getDSubstanceBusinessInfoListByInchikey, {
    manual: true,
    debounceWait: 300,
    onError: () => {
      MessagePlugin.error('获取商家信息失败');
    },
    onSuccess: (res: any) => {
      console.log("商家列表数据", res);
      const { records, total } = res;
      businessList.value = records;
      pagination.value = {
        ...pagination.value,
        total,
      };

      if (records.length === 0 && total !== 0) {
        pagination.value.current = 1;
        // 为每个商家初始化表格数据
        initBusinessTables();
      }
    },
  }),


};

// 初始化商家表格数据
const initBusinessTables = () => {
  businessList.value.forEach(business => {
    businessTables.value.set(business.id, {
      businessInfo: business,
      detailList: [],
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      loading: false,
    });
    // 获取该商家的详细产品数据
    // loadBusinessDetails(business.id);
  });
};

// 加载商家详细信息
const loadBusinessDetails = async (biId: number) => {
  const tableData = businessTables.value.get(biId);
  if (tableData) {
    try {
      tableData.loading = true;

      const requestParams = {
        param: {
          biId: biId,
          inchikey: state.detailItem.baseCode,
          // 可以在这里添加筛选条件
          ...state.filterParams
        },
        current: tableData.pagination.current,
        pageSize: tableData.pagination.pageSize,
      };

      console.log(`加载商家${biId}的详细信息`, requestParams);

      const res = await getDSubstanceBusinessInfoDetailsListByPage(requestParams);

      console.log(`商家${biId}详细信息数据`, res);

      // 处理返回的数据格式
      if (Array.isArray(res)) {
        // 如果直接返回数组
        tableData.detailList = res;
        tableData.pagination = {
          ...tableData.pagination,
          total: res.length,
        };
      } else {
        // 如果返回分页对象
        const { records, total } = res;
        tableData.detailList = records || [];
        tableData.pagination = {
          ...tableData.pagination,
          total: total || 0,
        };
      }

    } catch (error) {
      console.error(`获取商家${biId}详细信息失败:`, error);
      MessagePlugin.error(`获取商家${biId}详细信息失败`);
    } finally {
      tableData.loading = false;
    }
  }
};

// 分页变化处理
const handlePageChange = (biId: number) => (pageInfo: PageInfo) => {
  const tableData = businessTables.value.get(biId);
  if (tableData) {
    tableData.pagination.current = pageInfo.current;
    tableData.pagination.pageSize = pageInfo.pageSize;
    loadBusinessDetails(biId);
  }
};

// 打开网站
const openWebsite = (url: string) => {
  if (url) {
    // 确保URL包含协议
    const finalUrl = url.startsWith('http') ? url : `https://${url}`;
    window.open(finalUrl, '_blank');
  }
};

// 初始化字典数据
const initDictData = async () => {
  try {
    const res = await getDictItems('BUSINESS_INFO');
    console.log('字典数据', res);

    // 按 value 字段前缀分组
    const groupedData: any = {
      specification: [],
      purity: [],
      price: [],
      availability: [],
      brand: []
    };

    res.forEach((item: any) => {
      const value = item.value || '';

      if (value.startsWith('packaging_unit_')) {
        // 规格
        groupedData.specification.push({
          text: item.label,
          value: item.label
        });
      } else if (value.startsWith('quality_standard_')) {
        // 纯度
        groupedData.purity.push({
          text: item.label,
          value: item.label
        });
      } else if (value.startsWith('price_')) {
        // 价格
        groupedData.price.push({
          text: item.label,
          value: item.label
        });
      } else if (value.startsWith('delivery_time_')) {
        // 是否现货
        groupedData.availability.push({
          text: item.label,
          value: item.label
        });
      } else if (value.startsWith('brand_')) {
        // 品牌
        groupedData.brand.push({
          text: item.label,
          value: item.label
        });
      }
    });

    dictData.value = groupedData;
    console.log('按前缀分组后的字典数据', dictData.value);

  } catch (error) {
    console.error('获取字典数据失败:', error);
    MessagePlugin.error('获取筛选条件数据失败');
  }
};

// 筛选条件变化处理
const handleFilterChange = (filterType: string, value: string) => {
  selectedFilters.value[filterType as keyof typeof selectedFilters.value] = value;
  console.log('筛选条件变化', filterType, value, selectedFilters.value);

  // 如果选择了价格筛选项，清空自定义价格区间
  if (filterType === 'price' && value) {
    customPriceRange.value = { min: '', max: '' };
  }

  // 重新加载所有商家的数据
  businessTables.value.forEach((tableData, biId) => {
    tableData.pagination.current = 1; // 重置到第一页
    loadBusinessDetails(biId);
  });
};

// 自定义价格区间变化处理
const handleCustomPriceChange = () => {
  console.log('自定义价格区间变化', customPriceRange.value);

  // 如果有自定义价格输入，清空价格筛选选项
  if (customPriceRange.value.min || customPriceRange.value.max) {
    selectedFilters.value.price = '';
  }

  // 重新加载所有商家的数据
  businessTables.value.forEach((tableData, biId) => {
    tableData.pagination.current = 1; // 重置到第一页
    loadBusinessDetails(biId);
  });
};

// 重置筛选条件
const resetFilters = () => {
  selectedFilters.value = {
    specification: '',
    purity: '',
    price: '',
    availability: '',
    brand: ''
  };

  // 重置自定义价格区间
  customPriceRange.value = {
    min: '',
    max: ''
  };

  // 重新加载所有商家的数据
  businessTables.value.forEach((tableData, biId) => {
    tableData.pagination.current = 1; // 重置到第一页
    loadBusinessDetails(biId);
  });
};

// 初始化数据的方法
const initializeData = async () => {
  try {
    state.loading = true;

    // 先初始化字典数据
    await initDictData();

    const { run, loading } = reqRunner.getBusinessList;
    state.loading = loading;
    // 调用API获取商家列表
    run({
      param: { inchikey: state.detailItem.baseCode },
      pageNo: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });

  } catch (error) {
    console.error('获取商家数据失败:', error);
    MessagePlugin.error('获取商家数据失败');
  } finally {
    state.loading = false;
  }
};

// 表格列定义
const getTableColumns = () => [
  {
    colKey: 'companyName',
    title: '公司名',
    width: 120,
    ellipsis: true,
  },
  {
    colKey: 'webUrl',
    title: '网址',
    width: 120,
    ellipsis: true,
  },
  {
    colKey: 'address',
    title: '地址',
    width: 120,
    ellipsis: true,
  },
  {
    colKey: 'contactNumber',
    title: '联系电话',
    width: 120,
    ellipsis: true,
  },
  {
    colKey: 'products',
    title: '产品列表',
    width: 120,
    ellipsis: true,
  },
  {
    colKey: 'infoSources',
    title: '信息来源',
    width: 120,
    ellipsis: true,
  },
]
// const getTableColumns = () => [
//   {
//     colKey: 'brand',
//     title: '品牌',
//     width: 120,
//     ellipsis: true,
//   },
//   {
//     colKey: 'itemNumber',
//     title: '产品编号',
//     width: 140,
//     ellipsis: true,
//   },
//   {
//     colKey: 'packagingUnit',
//     title: '规格',
//     width: 100,
//     ellipsis: true,
//   },
//   {
//     colKey: 'deliveryTime',
//     title: '交货时间',
//     width: 120,
//     ellipsis: true,
//   },
//   {
//     colKey: 'qualityStandard',
//     title: '纯度',
//     width: 100,
//     ellipsis: true,
//   },
//   {
//     colKey: 'delPrice',
//     title: '交易价格',
//     width: 120,
//     ellipsis: true,
//   },
//   {
//     colKey: 'price',
//     title: '价格',
//     width: 100,
//     ellipsis: true,
//   },
//   {
//     colKey: 'calculatePrice',
//     title: '计算价格',
//     width: 120,
//     ellipsis: true,
//   },
// ];

/**
 * table分页变化时调用
 * @param pageInfo
 */
const rehandlePageChange = (pageInfo: PageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  initializeData();
};

// 组件挂载时初始化数据
onMounted(() => {
  initializeData();
});
</script>

<template>
  <div class="substance-business-info">
    <div v-if="state.loading" class="loading-container">
      <div class="loading-spinner">加载商家信息中...</div>
    </div>

    <template v-else>
      <!-- 商家信息区域 -->
      <div class="business-section">
        <h4>商家信息 (共 {{ businessList.length }} 家)</h4>
      </div>
      <div class="business-table">
        <Table :data="businessList || []" :columns="getTableColumns()" :bordered="true" :hover="true" :stripe="true"
          row-key="id" table-layout="fixed" cell-empty-content="-" />
      </div>
      <div v-if="businessList.length === 0" class="empty-state">
        <p>暂无商家信息</p>
      </div>
      <br />
      <!-- 分页 -->
      <Pagination v-model:current="pagination.current" :total="pagination.total" :page-size="pagination.pageSize"
        @change="rehandlePageChange" class="mt-4" v-if="pagination.total > 0" />
      <!-- 筛选条件区域 -->
      <!-- <div class="filter-section">
        <div class="filter-row"> -->
      <!-- 动态生成筛选条件 -->
      <!-- <div 
            v-for="(filterType, index) in Object.keys(dictData)" 
            :key="filterType"
            class="filter-item"
          >
            <span class="filter-label">{{ filterLabels[filterType] }}</span>
            <div class="filter-options"> -->
      <!-- 全部选项 -->
      <!-- <button 
                :class="['filter-btn', { active: selectedFilters[filterType] === '' && (filterType !== 'price' || (!customPriceRange.min && !customPriceRange.max)) }]"
                @click="handleFilterChange(filterType, '')"
              >
                全部
              </button> -->
      <!-- 字典选项 -->
      <!-- <button 
                v-for="option in dictData[filterType]" 
                :key="option.value"
                :class="['filter-btn', { active: selectedFilters[filterType] === option.value }]"
                @click="handleFilterChange(filterType, option.value)"
              >
                {{ option.text }}
              </button> -->

      <!-- 价格自定义输入框 -->
      <!-- <template v-if="filterType === 'price'">
                <input 
                  v-model="customPriceRange.min"
                  type="number"
                  placeholder="最低价"
                  class="price-input"
                  @input="handleCustomPriceChange"
                />
                <span class="price-separator">-</span>
                <input 
                  v-model="customPriceRange.max"
                  type="number"
                  placeholder="最高价"
                  class="price-input"
                  @input="handleCustomPriceChange"
                />
              </template>
</div>
</div>
</div>

<div class="filter-actions">
  <button class="reset-btn" @click="resetFilters">重置</button>
</div>
</div> -->

      <!-- 商家信息区域 -->
      <!-- <div class="business-section">
        <h4>商家信息 (共 {{ businessList.length }} 家)</h4> -->

      <!-- 遍历每个商家显示对应的表格 -->
      <!-- <div 
          v-for="(business, index) in businessList" 
          :key="business.id"
          class="business-table-container"
        > -->
      <!-- 商家基本信息 -->
      <!-- <div class="business-header">
            <div class="business-info">
              <div class="company-name">
                <ShopIcon class="company-icon" />
                {{ business.companyName || '-' }}
                <Tooltip v-if="business.webUrl" :content="business.webUrl" placement="top">
                  <InternetIcon 
                    class="website-icon" 
                    @click="openWebsite(business.webUrl)"
                  />
                </Tooltip>
                <span v-if="business.address" class="detail-item" style="margin-left: 20px;">
                  <LocationIcon class="detail-icon" />
                  {{ business.address }}
                </span>
                <span v-if="business.contactNumber" class="detail-item" style="margin-left: 20px;">
                  <CallIcon class="detail-icon" />
                  {{ business.contactNumber }}
                </span>
              </div> -->
      <!-- <div class="company-details">
                <span v-if="business.address" class="detail-item">
                  <LocationIcon class="detail-icon" />
                  {{ business.address }}
                </span>
                <span v-if="business.contactNumber" class="detail-item">
                  <CallIcon class="detail-icon" />
                  {{ business.contactNumber }}
                </span>
                <span v-if="business.webUrl" class="detail-item">
                  <InternetIcon class="detail-icon" />
                  <a :href="business.webUrl" target="_blank" class="web-link">{{ business.webUrl }}</a>
                </span>
              </div>
            </div>
          </div> -->

      <!-- 商家产品表格 -->
      <!-- <div class="business-table">
            <Table
              :data="businessTables.get(business.id)?.detailList || []"
              :columns="getTableColumns()"
              :loading="businessTables.get(business.id)?.loading || false"
              :pagination="businessTables.get(business.id)?.pagination"
              :bordered="true"
              :hover="true"
              :stripe="true"
              row-key="id"
              table-layout="fixed"
              cell-empty-content="-"
              @page-change="handlePageChange(business.id)"
            />
          </div>
        </div>
        
        <div v-if="businessList.length === 0" class="empty-state">
          <p>暂无商家信息</p>
        </div>
      </div> -->
    </template>
  </div>
</template>

<style scoped lang="scss">
.substance-business-info {
  padding: 20px;

  // 加载状态样式
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-spinner {
      font-size: 16px;
      color: #666;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid #e8e8e8;
        border-top: 2px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  @keyframes spin {
    0% {
      transform: translateY(-50%) rotate(0deg);
    }

    100% {
      transform: translateY(-50%) rotate(360deg);
    }
  }

  // 筛选条件样式
  .filter-section {
    background: #f8f9fa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    .filter-row {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 16px;
    }

    .filter-item {
      display: flex;
      align-items: center;
      gap: 12px;

      .filter-label {
        font-weight: 500;
        color: #333;
        min-width: 60px;
        font-size: 14px;
      }

      .filter-options {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .price-input {
          padding: 6px 8px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          font-size: 12px;
          width: 80px;

          &::placeholder {
            color: #999;
          }

          &:focus {
            border-color: var(--td-brand-color-hover, #1976d2);
            outline: none;
          }
        }

        .price-separator {
          color: #666;
          font-size: 14px;
          margin: 0 4px;
        }
      }

      .filter-btn {
        padding: 6px 12px;
        border: 1px solid #d9d9d9;
        background: #fff;
        border-radius: 4px;
        color: #666;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--td-brand-color-hover, #1976d2);
          color: var(--td-brand-color-hover, #1976d2);
        }

        &.active {
          background: var(--td-brand-color-hover, #1976d2);
          border-color: var(--td-brand-color-hover, #1976d2);
          color: #fff;
        }
      }


    }

    .filter-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;

      .reset-btn {
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        background: #fff;
        border-radius: 4px;
        color: #666;
        cursor: pointer;
        font-size: 14px;

        &:hover {
          border-color: var(--td-brand-color-hover, #1976d2);
          color: var(--td-brand-color-hover, #1976d2);
        }
      }


    }
  }

  // 商家信息样式
  .business-section {
    h4 {
      font-size: 16px;
      color: #333;
      margin-bottom: 15px;
      display: flex;
      align-items: center;

      &::before {
        content: '▼';
        margin-right: 10px;
      }
    }

    .business-table-container {
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      margin-bottom: 20px;
      overflow: hidden;

      .business-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e8e8e8;
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .business-info {
          flex: 1;

          .company-name {
            font-size: 16px;
            // font-weight: 500;
            color: #333;
            // margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;

            .company-icon {
              color: #1890ff;
              font-size: 18px;
            }

            .website-icon {
              color: var(--td-brand-color-hover, #1976d2);
              font-size: 16px;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                color: var(--td-brand-color-hover, #1976d2);
                transform: scale(1.1);
              }
            }
          }

          .company-details {
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: #666;

            .detail-item {
              white-space: nowrap;
              display: flex;
              align-items: center;
              gap: 4px;

              .detail-icon {
                font-size: 14px;
                color: #999;
              }
            }

            .web-link {
              color: #1890ff;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }
          }
        }

        .company-phone {
          font-size: 14px;
          color: #333;
          margin-right: 20px;
        }

        .company-public {
          color: #1890ff;
          cursor: pointer;
          font-size: 14px;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .business-table {
        .pagination-container {
          padding: 16px;
          display: flex;
          justify-content: flex-end;
          border-top: 1px solid #e8e8e8;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  @media (max-width: 768px) {
    padding: 15px;

    .filter-section {
      padding: 12px;

      .filter-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }

    .business-header {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 12px;

      .company-details {
        flex-direction: column !important;
        gap: 8px !important;
      }
    }
  }
}
</style>
