.container {
  width: 100%;
  height: 300px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.bannerWrapper {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 20px;
}

.navButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #ffffff;
  border: 2px solid #e0e0e0;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover:not(:disabled) {
    background: #f0f8ff;
    border-color: #4a90e2;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f5f5f5;
    border-color: #d0d0d0;
  }

  svg {
    width: 20px;
    height: 20px;
    color: #666;
    transition: color 0.3s ease;
  }

  &:hover:not(:disabled) svg {
    color: #4a90e2;
  }
}

.canvasContainer {
  flex: 1;
  height: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.canvas {
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.01);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 15px;
    height: 250px;
  }

  .bannerWrapper {
    gap: 10px;
  }

  .navButton {
    width: 35px;
    height: 35px;

    svg {
      width: 16px;
      height: 16px;
    }
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
    height: 200px;
  }

  .navButton {
    width: 30px;
    height: 30px;

    svg {
      width: 14px;
      height: 14px;
    }
  }
}
