import { useSearchStore } from '#/store/search';
import { useUserStore } from '@vben/stores';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';

import { useApprovalModal } from './useApprovalModal';
// 定义 ListItem 类型，确保 listData 类型安全
export interface ListItem {
  id: number | string;
  // 基本信息
  dataName?: string; // 数据名称
  dataImg?: string; // 数据图片
  dataDescri?: string; // 数据描述
  author?: string; // 作者
  cas?: string; // CAS号
  molecularFormula?: string; // 分子式
  categoryName?: string; // 分类名称
  browsingCount?: number; // 浏览次数
  baseCode?: string; // 基础代码，用于详情和下载
  fileType?: string; // 文件类型
  // 原有的 infomation 对象（保持向后兼容）
  infomation: {
    abstract?: string; // 摘要
    author?: string;
    browsingCount?: number;
    cas?: string; // CAS号
    categoryName?: string; // 分类名称
    data_name: string;
    dataCreateTime?: string; // 数据创建时间
    dataName?: string; // 添加 dataName 字段
    describe?: string;
    img: string;
    molecularFormula?: string; // 分子式
    molecularWeight?: string; // 分子量
  };

  // 时间和状态
  createTime: string;
  isFavorite: boolean;
  isVisible?: number | string; // 审批状态：1-审批通过，0-待审批

  // 标签和分类
  tags: string[];
  labelName?: string; // 标签名称（逗号分隔）

  // 数据类型和权限
  dataType_text?: string;
  dataAuth_text?: string;
  dataType?: number | string;
  dataAuth?: number | string;
}

export function usePermissions(onSuccess?: () => void) {
  const userStore = useUserStore();
  const vueRouter = useRouter();
  const searchStore = useSearchStore();
  const { openModal } = useApprovalModal();

  // 跳转到详情页
  function navigateToDetail(item: ListItem) {
    // 存储 item 到 pinia
    searchStore.setCurrentDetailItem(item);
    // 跳转到详情页
    vueRouter.push({ path: '/details' });
  }

  // 显示审批弹窗
  function showApprovalModal(item: ListItem) {
    openModal(item, onSuccess);
  }

  // 资源点击处理逻辑
  function handleResourceAccess(item: any) {
    const targetItem = item?.row || item;
    const sourceSystemCode = (userStore.userInfo as any)?.sourceSystemCode;
    const { dataAuth, isVisible } = targetItem;
    const authCode = dataAuth && String(dataAuth);
    const visibleStatus = Number(isVisible);

    if (!authCode) {
      MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
      return;
    }

    const isInternalUser =
      sourceSystemCode === '01' || sourceSystemCode === '02';
    if (isInternalUser) {
      // 内部用户
      if (authCode === '0') {
        // 完全共享
        navigateToDetail(targetItem);
      } else if (visibleStatus === 1) {
        // 审批通过
        navigateToDetail(targetItem);
      } else if (visibleStatus === 0 && authCode === '1') {
        // 待审批的审批共享
        showApprovalModal(targetItem);
      } else {
        // 暂不共享
        MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
      }
    } else {
      // 外部用户
      if (authCode === '2') {
        MessagePlugin.warning('该资源暂不共享，请选择其他资源。');
        return;
      }

      if (visibleStatus === 1) {
        // 审批通过
        navigateToDetail(targetItem);
      } else {
        // 待审批
        showApprovalModal(targetItem);
      }
    }
  }

  return {
    handleResourceAccess,
  };
}
