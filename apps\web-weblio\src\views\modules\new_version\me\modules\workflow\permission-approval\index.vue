<script setup lang="ts">
import {
  Button,
  DatePicker,
  Dialog,
  Form,
  FormItem,
  Input,
  MessagePlugin,
  Space,
  Textarea,
} from 'tdesign-vue-next';
import { reactive, ref } from 'vue';

import { executeTask, rejectTask } from './api';

// 定义事件
const emit = defineEmits(['refresh']);

// 弹窗显示状态
const visible = ref(false);

// loading状态
const approveLoading = ref(false);
const rejectLoading = ref(false);

const isOffline = ref(false);

// 表单数据
const formData = reactive({
  operation_name: '', // 资料名称
  start_time: '', // 权限生效时间
  end_time: '', // 权限失效时间
  remark: '', // 备注
  applicant_name: '',
  corporate_name: '',
  career: '',
  phone_number: '',
  applicant_email: '',
});

// 当前处理的记录
const currentRecord = ref<any>(null);

// 打开弹窗
const openDialog = (record: any) => {
  const { variable } = record;
  const variableData = (variable && JSON.parse(variable)) || false;
  if (!variableData) {
    return;
  }
  currentRecord.value = record;
  isOffline.value = variableData.is_offline === 1;
  // 填充表单数据
  formData.operation_name = variableData.operation_name || '';
  formData.start_time = variableData.start_time || '';
  formData.end_time = variableData.end_time || '';
  formData.remark = variableData.remark || '';
  if (isOffline.value) {
    formData.applicant_name = variableData.applicant_name || '';
    formData.corporate_name = variableData.corporate_name || '';
    formData.career = variableData.career || '';
    formData.phone_number = variableData.phone_number || '';
    formData.applicant_email = variableData.applicant_email || '';
  }
  visible.value = true;
};

// 关闭弹窗
const closeDialog = () => {
  visible.value = false;
  currentRecord.value = null;
  isOffline.value = false;
  // 重置表单
  Object.assign(formData, {
    operation_name: '',
    start_time: '',
    end_time: '',
    remark: '',
    applicant_name: '',
    corporate_name: '',
    career: '',
    phone_number: '',
    applicant_email: '',
  });
};

// 通过审批
const handleApprove = async () => {
  try {
    approveLoading.value = true;
    await executeTask({
      id: currentRecord.value.taskId,
      args: currentRecord.value.arg,
    });
    MessagePlugin.success('审批通过！');

    closeDialog();
    // 触发父组件刷新
    emit('refresh');
  } catch {
    MessagePlugin.error('审批失败，请重试');
  } finally {
    approveLoading.value = false;
  }
};

// 驳回审批
const handleReject = async () => {
  try {
    rejectLoading.value = true;
    await rejectTask({
      id: currentRecord.value.taskId,
      args: currentRecord.value.arg,
    });
    MessagePlugin.success('驳回成功！');
    closeDialog();
    // 触发父组件刷新
    emit('refresh');
  } catch {
    MessagePlugin.error('驳回失败，请重试');
  } finally {
    rejectLoading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  openDialog,
});
</script>

<template>
  <Dialog
    v-model:visible="visible"
    title="权限审批"
    width="600px"
    :z-index="9999"
    placement="center"
    :show-overlay="true"
    :close-on-overlay-click="false"
    @close="closeDialog"
    attach="body"
  >
    <Form :data="formData" layout="vertical" class="permission-form" readonly>
      <FormItem label="名称" name="operation_name">
        <Input v-model="formData.operation_name" placeholder="请输入资料名称" />
      </FormItem>
      <template v-if="isOffline">
        <FormItem label="申请人" name="applicant_name">
          <Input v-model="formData.applicant_name" />
        </FormItem>
        <FormItem label="公司名" name="corporate_name">
          <Input v-model="formData.corporate_name" />
        </FormItem>
        <FormItem label="职位" name="career">
          <Input v-model="formData.career" />
        </FormItem>
        <FormItem label="电话" name="phone_number">
          <Input v-model="formData.phone_number" />
        </FormItem>
        <FormItem label="邮箱" name="applicant_email">
          <Input v-model="formData.applicant_email" />
        </FormItem>
      </template>

      <FormItem
        label="权限生效时间"
        name="startTime"
        v-if="formData.start_time"
      >
        <DatePicker
          v-model="formData.start_time"
          placeholder="请输入权限生效时间"
        />
      </FormItem>

      <FormItem label="权限失效时间" name="endTime" v-if="formData.end_time">
        <DatePicker
          v-model="formData.end_time"
          placeholder="请输入权限失效时间"
        />
      </FormItem>

      <FormItem label="备注" name="remark" v-if="formData.remark">
        <Textarea
          v-model="formData.remark"
          placeholder="请输入备注信息"
          :maxlength="500"
          :autosize="{ minRows: 3, maxRows: 6 }"
        />
      </FormItem>
    </Form>

    <template #footer>
      <div class="dialog-footer">
        <Space>
          <Button
            theme="primary"
            @click="handleApprove"
            :loading="approveLoading"
            :disabled="rejectLoading"
          >
            通过
          </Button>
          <Button
            theme="danger"
            @click="handleReject"
            :loading="rejectLoading"
            :disabled="approveLoading"
          >
            驳回
          </Button>
          <Button
            variant="outline"
            @click="closeDialog"
            :disabled="approveLoading || rejectLoading"
          >
            取消
          </Button>
        </Space>
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
.permission-form {
  padding: 20px 0;
  max-height: 400px;
  overflow-y: auto;
}

.permission-form :deep(.t-form__item) {
  margin-bottom: 20px;
}

.permission-form :deep(.t-form__label) {
  font-weight: 500;
  color: #333;
}

.dialog-footer {
  padding: 16px 0;
  text-align: right;
  border-top: 1px solid #e7e7e7;
  margin-top: 20px;
}

/* 确保弹窗在最顶层 */
:deep(.t-dialog) {
  z-index: 9999 !important;
}

:deep(.t-dialog__mask) {
  z-index: 9998 !important;
}
</style>
