import { MessagePlugin } from 'tdesign-vue-next';
import { defineComponent } from 'vue';

import styles from './ShareDialog.module.less';

export default defineComponent({
  name: 'ShareDialog',
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    shareUrl: {
      type: String,
      required: true,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const closeShareDialog = () => {
      emit('update:visible', false);
    };

    const copyLink = () => {
      if (props.shareUrl) {
        navigator.clipboard.writeText(props.shareUrl);
        MessagePlugin.success('链接复制成功');
      }
    };

    return () =>
      props.visible ? (
        <div class={styles.shareDialogMask} onClick={closeShareDialog}>
          <div
            class={styles.shareDialogBox}
            onClick={(e: Event) => e.stopPropagation()}
          >
            <div class={styles.shareDialogTitle}>数据分享链接</div>
            <div class={styles.shareDialogContent}>
              <input
                class={styles.shareDialogInput}
                readonly
                value={props.shareUrl}
              />
              <button class={styles.shareDialogCopy} onClick={copyLink}>
                复制链接
              </button>
            </div>
            <div class={styles.shareDialogFooter}>
              <button
                class={styles.shareDialogClose}
                onClick={closeShareDialog}
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      ) : null;
  },
});
