.breadcrumb-container {
  padding: 20px 0 !important;
  background-color: #0f569f;
  :global(.t-breadcrumb) {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }
  :global(.t-breadcrumb__inner-text) {
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
  }
  :global(.t-breadcrumb__separator) {
    color: #ffffff;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .breadcrumb-container {
    :global(.t-breadcrumb) {
      padding: 0 16px;
    }
  }
}

@media (max-width: 480px) {
  .breadcrumb-container {
    :global(.t-breadcrumb) {
      padding: 0 16px;
    }
  }
}
