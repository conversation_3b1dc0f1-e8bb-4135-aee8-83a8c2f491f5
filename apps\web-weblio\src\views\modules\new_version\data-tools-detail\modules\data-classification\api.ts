import { requestClient } from '#/api/request';

// 数据整编与分类相关API接口 - 基于标准化接口文档

/**
 * 获取整编字典映射信息
 */
export async function getFieldDict() {
  return requestClient.get('/rgdc-sys/dataTools/get_field_dict');
}

/**
 * 通过DOI进行数据整编
 * @param data FormData containing dois_file and data parameters
 */
export async function queryByDois(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/query_by_dois', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 120_000,
  });
}

/**
 * 获取整编任务列表
 * @param params 查询参数
 */
export async function getTasksList(params: {
  page: number;
  page_size: number;
  task_name?: string;
}) {
  return requestClient.get('/rgdc-sys/dataTools/tasks_list', {
    params,
  });
}

/**
 * 获取整编任务详情
 * @param id 任务ID
 */
export async function getTasksDetail(id?: number) {
  return requestClient.get('/rgdc-sys/dataTools/tasks_detail', {
    params: id ? { id } : {},
  });
}

/**
 * 下载整编Excel表格模板
 */
export async function downloadTemplate() {
  return requestClient.get('/rgdc-sys/dataTools/download_template', {
    responseType: 'blob',
  });
}

/**
 * 下载整编任务的Excel文件（一键下载）
 * @param task_id 任务ID
 */
export async function downloadExcel(task_id?: number) {
  return requestClient.get('/rgdc-sys/dataTools/download_excel', {
    params: task_id ? { task_id } : {},
    responseType: 'blob',
  });
}

/**
 * 下载整编任务的单个数据项文件
 * @param params 下载参数
 */
export async function downloadSingle(params: {
  task_id?: string;
  id?: string;
  file_type?: 'json' | 'excel';
}) {
  return requestClient.get('/rgdc-sys/dataTools/download_single', {
    params,
    responseType: 'blob',
  });
}

// 兼容性函数 - 保持向后兼容
/**
 * 创建分类任务 (兼容性函数)
 * @param data 任务数据
 */
export async function createClassificationTask(data: FormData) {
  return queryByDois(data);
}

/**
 * 获取任务列表 (兼容性函数)
 * @param params 查询参数
 */
export async function getTaskList(params: { page: number; page_size: number }) {
  return getTasksList(params);
}

/**
 * 获取任务详情 (兼容性函数)
 * @param taskId 任务ID
 */
export async function getTaskDetail(taskId: string) {
  return getTasksDetail(parseInt(taskId));
}

/**
 * 获取元数据字段配置 (兼容性函数)
 */
export async function getMetadataFields() {
  return getFieldDict();
}
