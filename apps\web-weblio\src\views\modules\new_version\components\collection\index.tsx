import type { PropType } from 'vue';

import { saveFavorites } from '#/views/modules/tPortalFavorites/api';
import { HeartFilledIcon, HeartIcon } from 'tdesign-icons-vue-next';
import { Button, MessagePlugin } from 'tdesign-vue-next';
import { defineComponent, ref, withModifiers } from 'vue';

import styles from './style.module.less';

export default defineComponent({
  name: 'Collection',
  props: {
    isFavorite: {
      type: Boolean,
      required: true,
    },
    row: {
      type: Object as PropType<any>,
      required: true,
    },
  },
  emits: ['update:isFavorite', 'collectionChange'],
  setup(props, { emit }) {
    const localFavorite = ref(props.isFavorite);

    const handleCollection = async () => {
      const prev = localFavorite.value;
      localFavorite.value = !prev;
      try {
        const {
          dataType,
          baseCode,
          infomation,
          dataName,
          dataAuth,
          dataDescri,
          dataImg,
          isFile,
        } = props.row;
        await saveFavorites({
          dataName,
          dataAuth,
          dataDescri,
          dataImg,
          isFile,
          dataType,
          operationCode: baseCode,
          dataInformation: JSON.stringify(infomation) || '',
          isDeleted: +!localFavorite.value,
        });
        emit('update:isFavorite', localFavorite.value);
        emit('collectionChange', {
          isFavorite: localFavorite.value,
          row: props.row,
        });
        MessagePlugin.success(
          localFavorite.value ? '收藏成功' : '取消收藏成功',
        );
      } catch {
        localFavorite.value = prev;
        MessagePlugin.error('操作失败');
      }
    };

    return () => (
      <Button
        class={styles.collectionBtn}
        onClick={withModifiers(handleCollection, ['stop'])}
        shape="circle"
        theme="default"
        variant="text"
      >
        {localFavorite.value ? (
          <HeartFilledIcon size="20" style={{ color: '#FF4D4F' }} />
        ) : (
          <HeartIcon size="20" style={{ color: '#C0C4CC' }} />
        )}
      </Button>
    );
  },
});
