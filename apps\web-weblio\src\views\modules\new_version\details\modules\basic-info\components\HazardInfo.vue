<script setup lang="ts">
import {
  HeartIcon,
  ShareIcon,
  ViewListIcon,
  ViewModuleIcon,
  CalculatorIcon,
  ChevronLeftIcon
} from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  Loading,
  Pagination,
  Select,
  Table,
  Tag,
} from 'tdesign-vue-next';
import { computed, onMounted, reactive, ref } from 'vue';

import {
  getHazardInfoData
} from '../api';
import { useSearchStore } from '#/store/search';

interface HazardInfoData {
  // sign: string,
  // wgkGermany: string,
  // transportNumber: string,
  // customsCode: string,
  // hazardSigns: string,
  // rtecs: string,
  // packingGroup: string,
  // hazardClasses: string,
  // safetyInstructions: string,
  // hazardClassesCode: string,
  // hazardDescription: string,
  // hazardPreventionInstructions: string,
  isolationAndEvacuation: string,
  spillageDisposal: string,
  cleaningMethod: string,
  disposalMethods: string,
  preventiveMeasures: string,
  firstAid: string,
  fireFighting: string,
  fireFightingProcedures: string,
  fireHazard: string,
  nonfireSpillResponse: string,
  safeStorage: string,
  storageConditions: string,
  ghsClassification: string,
  hazardClassesAndCategories: string,
  epaHazardClasses: string,
  healthHazards: string,
  fireHazards: string,
  hazardsSummary: string,
  firePotential: string,
  skinEyeAndRespiratoryIrritations: string,
  airAndWaterReactions: string,
  reactiveGroup: string,
  reactivityProfile: string,
  dotLabel: string,
  transportationModeRegulations: string,
  packagingLabeling: string
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: false,

});

// 定义加载状态，用于控制页面加载动画显示
const loading = ref(true);

// 初始化危险识别数据对象，字段命名与接口定义保持一致，确保类型安全
const data = ref<HazardInfoData>({
  // sign: '',
  // wgkGermany: '',
  // transportNumber: '',
  // customsCode: '',
  // hazardSigns: '',
  // rtecs: '',
  // packingGroup: '',
  // hazardClasses: '',
  // safetyInstructions: '',
  // hazardClassesCode: '',
  // hazardDescription: '',
  // hazardPreventionInstructions: '',
  isolationAndEvacuation: '',
  spillageDisposal: '',
  cleaningMethod: '',
  disposalMethods: '',
  preventiveMeasures: '',
  firstAid: '',
  fireFighting: '',
  fireFightingProcedures: '',
  fireHazard: '',
  nonfireSpillResponse: '',
  safeStorage: '',
  storageConditions: '',
  ghsClassification: '',
  hazardClassesAndCategories: '',
  epaHazardClasses: '',
  healthHazards: '',
  fireHazards: '',
  hazardsSummary: '',
  firePotential: '',
  skinEyeAndRespiratoryIrritations: '',
  airAndWaterReactions: '',
  reactiveGroup: '',
  reactivityProfile: '',
  dotLabel: '',
  transportationModeRegulations: '',
  packagingLabeling: ''
})

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    console.log("当前详情数据", detail, 2222);
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    console.log("当前详情数据", local, 2222);
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

onMounted(async () => {
  try {
    // 调用危险识别信息查询API请求方法，传入 inchikey 参数
    const response = await getHazardInfoData({ inchikey: state.detailItem.baseCode });
    data.value = response;
  } catch (error) {
    console.error('获取危险识别信息失败:', error);
  } finally {
    // 关闭加载状态
    loading.value = false;
  }
});

const fieldLabels = {
  // sign: '危险品标志：',
  // wgkGermany: 'WGK Germany：',
  // transportNumber: '危险品运输编号：',
  // customsCode: '海关编码：',
  // hazardSigns: '危险标志：',
  // rtecs: 'RTECS号：',
  // packingGroup: '包装等级：',
  // hazardClasses: '危险类别：',
  // safetyInstructions: '安全说明：',
  // hazardClassesCode: '危险类别码：',
  // hazardDescription: '危险性描述：',
  // hazardPreventionInstructions: '危险性防范说明：',
  isolationAndEvacuation: '隔离和疏散：',
  spillageDisposal: '泄漏处理：',
  cleaningMethod: '清理方法：',
  disposalMethods: '处置方法：',
  preventiveMeasures: '预防措施：',
  firstAid: '急救：',
  fireFighting: '消防：',
  fireFightingProcedures: '灭火程序：',
  fireHazard: '消防危险：',
  nonfireSpillResponse: '非火灾泄漏响应：',
  safeStorage: '安全存储：',
  storageConditions: '储存条件：',
  ghsClassification: 'GHS分类：',
  hazardClassesAndCategories: '危险类别和种类：',
  epaHazardClasses: 'EPA危险分类：',
  healthHazards: '健康危害：',
  fireHazards: '火灾危险：',
  hazardsSummary: '危险概述：',
  firePotential: '火灾隐患：',
  skinEyeAndRespiratoryIrritations: '皮肤、眼睛和呼吸道刺激：',
  airAndWaterReactions: '空气和水的反应：',
  reactiveGroup: '反应基团：',
  reactivityProfile: '反应性概况：',
  dotLabel: 'DOT 标签：',
  transportationModeRegulations: '运输方式和规定：',
  packagingLabeling: '包装和标签：'
};
</script>

<template>
  <div class="hazard-info">
    <!-- 加载状态提示 -->
    <div v-if="loading" class="loading-container">
      <Loading />
    </div>
    <!-- 主体内容展示 -->
    <div v-else class="content">
      <div class="info-items">
        <div v-for="(label, key) in fieldLabels" :key="key">
          <!-- 如果 data[key] 存在，则显示对应信息 -->
          <div class="info-item">
            <!-- 字段标签 -->
            <span class="label">{{ label }}</span>
            <!-- 字段值 -->
            <span class="value">
              {{ data == null ? '' : data[key] }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.hazard-info {
  margin: 0 auto;
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
  font-size: 24px;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.structure-image {
  width: 100%;
  max-width: 400px;
  margin-bottom: 20px;
}

.structure-image img {
  width: 100%;
  height: auto;
}

.info-items {
  width: 80%;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 24px 0;
  border-bottom: 1px dashed #ddd;
}

.label {
  font-weight: bold;
  min-width: 240px;
  color: #555;
}

.value {
  flex-grow: 1;
  color: #333;
  word-break: break-all;
}

.smiles-link {
  color: #3498db;
  text-decoration: underline;
  cursor: pointer;
}

.icon-style {
  width: 25px;
  height: 25px;
  margin-right: 10px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
