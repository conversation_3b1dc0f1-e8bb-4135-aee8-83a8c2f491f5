import { defineConfig } from '@vben/vite-config';
import VueJsx from '@vitejs/plugin-vue-jsx';
// 10.151.17.244 -wangh
// 10.159.252.124
// 10.159.214.231 -lsz
export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      plugins: [VueJsx()],
      server: {
        proxy: {
          '/rgdc-search': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-search/, ''),
            target: 'http://10.159.252.124:8084/rgdc-search',
            ws: true,
          },
          '/rgdc-statistics': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-statistics/, ''),
            target: 'http://10.151.17.244:8085/rgdc-statistics-wangh',
            ws: true,
          },
          '/rgdc-submit': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-submit/, ''),
            target: 'http://10.151.17.244:8083/rgdc-submit-wangh',
            ws: true,
          },
          '/rgdc-sys': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-sys/, ''),
            target: 'http://10.151.17.244:8080/rgdc-sys-wangh',
            ws: true,
          },
          '/rgdc-user': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-user/, ''),
            target: 'http://10.151.17.244:8081/rgdc-user-wangh',
            ws: true,
          },
          '/rgdc-workflow': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rgdc-workflow/, ''),
            target: 'http://10.151.17.244:8082/rgdc-workflow-wangh',
            ws: true,
          },
        },
      },
    },
  };
});
